# نافذة تحليل المنتجات الذكي
# تعرض المنتجات الأكثر ربحية والأقل أداءً وتقترح إجراءات
from PyQt5 import QtWidgets, QtCore, QtGui
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import matplotlib.pyplot as plt
import matplotlib
# تعيين خط يدعم العربية لجميع عناصر الرسم البياني
matplotlib.rcParams['font.family'] = 'Tahoma'
# دعم الخط العربي بشكل صحيح
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
import pandas as pd
import sqlite3
from utils.utilities import get_cogs_fifo
from main import get_app_icon

class ProductAnalysisWindow(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('تحليل المنتجات الذكي')
        self.setGeometry(540, 270, 750, 520)
        # تعيين أيقونة البرنامج لجميع النوافذ
        self.setWindowIcon(get_app_icon())
        self.setup_ui()
        self.load_and_analyze()

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout(self)
        self.figure = plt.Figure(figsize=(7,4))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        self.status_label = QtWidgets.QLabel('')
        layout.addWidget(self.status_label)
        self.suggestion_label = QtWidgets.QLabel('')
        self.suggestion_label.setStyleSheet('color: #e53935; font-size: 13pt; font-weight: bold;')
        layout.addWidget(self.suggestion_label)
        # زر تصدير النتائج إلى Excel
        self.export_btn = QtWidgets.QPushButton('تصدير النتائج إلى Excel')
        self.export_btn.setStyleSheet('background-color: #1565c0; color: white; font-size: 12pt; font-weight: bold; border-radius: 7px; padding: 7px 15px;')
        self.export_btn.clicked.connect(self.export_to_excel)
        layout.addWidget(self.export_btn)
        # زر طباعة التحليل
        self.print_btn = QtWidgets.QPushButton('طباعة التحليل')
        self.print_btn.setStyleSheet('background-color: #43a047; color: white; font-size: 12pt; font-weight: bold; border-radius: 7px; padding: 7px 15px;')
        self.print_btn.clicked.connect(self.print_current_analysis)
        layout.addWidget(self.print_btn)

    def load_and_analyze(self):
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        # جلب المنتجات وكمياتها ومبيعاتها
        df = pd.read_sql_query('''SELECT p.id, p.name, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales \
                                 FROM products p \
                                 LEFT JOIN sales_items si ON p.id=si.product_id \
                                 GROUP BY p.id''', conn)
        conn.close()
        if len(df) < 1:
            self.figure.clear()
            self.canvas.draw()
            self.status_label.setText('لا توجد بيانات كافية للتحليل.')
            self.suggestion_label.setText('')
            return
        df = df.fillna(0)
        # حساب الربح الفعلي لكل منتج باستخدام FIFO
        df['profit'] = df.apply(lambda row: row['sales'] - get_cogs_fifo(row['id'], row['qty']), axis=1)
        # المنتجات الأكثر ربحية
        top_profitable = df.sort_values('profit', ascending=False).head(3)
        # المنتجات الأقل أداءً
        least_profitable = df.sort_values('profit', ascending=True).head(3)
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        # التأكد من أن الخط العربي مفعل لكل النصوص في الرسم
        plt.rcParams['font.family'] = 'Tahoma'
        # رسم الأعمدة مع تمييز الألوان
        bar_width = 0.35
        x1 = range(len(top_profitable))
        x2 = range(len(top_profitable), len(top_profitable) + len(least_profitable))
        names = list(top_profitable['name']) + list(least_profitable['name'])
        # تجهيز أسماء المنتجات لدعم العربية بشكل صحيح
        def reshape_arabic(text):
            if ARABIC_SUPPORT:
                reshaped = arabic_reshaper.reshape(str(text))
                return get_display(reshaped)
            return str(text)
        names = [reshape_arabic(n) for n in names]
        profits = list(top_profitable['profit']) + list(least_profitable['profit'])
        colors = ['#43a047']*len(top_profitable) + ['#e53935']*len(least_profitable)
        bars = ax.bar(names, profits, color=colors, width=bar_width)
        # عرض القيم فوق الأعمدة
        for bar in bars:
            height = bar.get_height()
            ax.annotate(f'{height:.0f}',
                        xy=(bar.get_x() + bar.get_width() / 2, height),
                        xytext=(0, 5),
                        textcoords="offset points",
                        ha='center', va='bottom', fontsize=11, fontweight='bold')
        ax.set_ylabel(reshape_arabic('إجمالي الربح (محسوب بطريقة FIFO)'), fontsize=13, fontweight='bold', fontname='Tahoma')
        ax.set_title(reshape_arabic('تحليل ربحية المنتجات (الأكثر والأقل ربحية)'), fontsize=15, fontweight='bold', color='#1565c0', fontname='Tahoma')
        ax.tick_params(axis='x', labelrotation=15, labelsize=12)
        ax.legend([reshape_arabic('الأكثر ربحية'), reshape_arabic('الأقل ربحية')], loc='upper left', fontsize=11, prop={'family': 'Tahoma'})
        self.figure.tight_layout()
        self.canvas.draw()
        self.status_label.setText('تم تحليل المنتجات.')
        # Tooltip يوضح طريقة احتساب الربح
        self.status_label.setToolTip('الربح محسوب بطريقة FIFO (الوارد أولاً يخرج أولاً) بناءً على تكلفة البضاعة المباعة.')
        # اقتراحات ذكية
        suggestions = []
        for _, row in least_profitable.iterrows():
            if row['qty'] > 0:
                suggestions.append(f'المنتج "{row["name"]}" يحقق ربحاً ضعيفاً. اقترح عمل عرض ترويجي أو تخفيض.')
            else:
                suggestions.append(f'المنتج "{row["name"]}" لم يحقق مبيعات. اقترح إعادة النظر في تسويقه أو إزالته.')
        self.suggestion_label.setText('\n'.join(suggestions))

    def export_to_excel(self):
        # إعادة التحليل لضمان تحديث البيانات
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        df = pd.read_sql_query('''SELECT p.id, p.name, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales \
                                 FROM products p \
                                 LEFT JOIN sales_items si ON p.id=si.product_id \
                                 GROUP BY p.id''', conn)
        conn.close()
        if len(df) < 1:
            QtWidgets.QMessageBox.warning(self, 'تنبيه', 'لا توجد بيانات كافية للتصدير.')
            return
        df = df.fillna(0)
        df['profit'] = df.apply(lambda row: row['sales'] - get_cogs_fifo(row['id'], row['qty']), axis=1)
        df[['name', 'qty', 'sales', 'profit']].to_excel('تحليل_ربحية_المنتجات.xlsx', index=False)
        QtWidgets.QMessageBox.information(self, 'تم التصدير', 'تم تصدير النتائج إلى ملف Excel بنجاح.')
        
    def print_current_analysis(self):
        from utils.print_utils import print_invoice
        html = self.generate_analysis_html()
        print_invoice(html, self)

    def generate_analysis_html(self):
        items_html = ""
        for row in self.get_analysis_rows():
            items_html += f"<tr>{''.join(f'<td>{cell}</td>' for cell in row)}</tr>"
        html = f"""
        <html><head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}}</style></head><body dir='rtl'>
        <h2 style='text-align:center'>تحليل المنتجات</h2>
        <table>{items_html}</table></body></html>"""
        return html

    def get_analysis_rows(self):
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        df = pd.read_sql_query('''SELECT p.id, p.name, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales \
                                 FROM products p \
                                 LEFT JOIN sales_items si ON p.id=si.product_id \
                                 GROUP BY p.id''', conn)
        conn.close()
        if len(df) < 1:
            return []
        df = df.fillna(0)
        df['profit'] = df.apply(lambda row: row['sales'] - get_cogs_fifo(row['id'], row['qty']), axis=1)
        rows = []
        for _, row in df.iterrows():
            rows.append([row['id'], row['name'], row['qty'], row['sales'], row['profit']])
        return rows
