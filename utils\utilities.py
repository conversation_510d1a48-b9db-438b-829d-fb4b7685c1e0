# utilities.py
# دوال وأدوات مساعدة مشتركة لكل النظام
import os
import json
import sqlite3

def load_settings(selected_activity=None):
    """
    تحميل إعدادات النظام حسب النشاط المختار (مطعم، صيدلية، ...)
    """
    SETTINGS_FILES = {
        'restaurant': 'settings.json',
        'pharmacy': 'settings_pharmacy.json',
        'supermarket': 'settings_supermarket.json',
        'clinic': 'settings_clinic.json',
    }
    if not selected_activity:
        selected_activity = 'restaurant'
        if os.path.exists('activity.txt'):
            with open('activity.txt', encoding='utf-8') as f:
                selected_activity = f.read().strip()
    settings_file = SETTINGS_FILES.get(selected_activity, 'settings.json')
    if not os.path.exists(settings_file):
        # استخدم المسار الكامل للملف
        base_dir = os.path.dirname(os.path.abspath(__file__))
        settings_file = os.path.join(base_dir, '..', settings_file)
        settings_file = os.path.abspath(settings_file)
    if not os.path.exists(settings_file):
        raise FileNotFoundError(f"لم يتم العثور على ملف الإعدادات: {settings_file}")
    with open(settings_file, encoding='utf-8') as f:
        settings = json.load(f)
    return settings


def parse_permissions(permissions):
    """
    تحويل الصلاحيات إلى قائمة موحدة (من نص أو قائمة)
    """
    if permissions is None:
        return []
    elif isinstance(permissions, str):
        return [p.strip() for p in permissions.split(',') if p.strip()]
    elif isinstance(permissions, (list, tuple)):
        return list(permissions)
    return []

def get_user_permissions_from_db(user_id):
    """
    جلب جميع الصلاحيات للمستخدم من قاعدة البيانات (عمود permissions فقط)
    """
    import sqlite3
    from models import database
    conn = sqlite3.connect(database.DB_NAME)
    c = conn.cursor()
    c.execute('SELECT permissions FROM users WHERE id=?', (user_id,))
    row = c.fetchone()
    conn.close()
    if row and row[0]:
        return parse_permissions(row[0])
    return []

def has_permission(user_role, permissions, required):
    """
    التحقق من وجود صلاحية معينة للمستخدم (المدير دائمًا يملك كل الصلاحيات)
    """
    if user_role == 'manager':
        return True
    perms = [p.strip() for p in parse_permissions(permissions) if p.strip()]
    return required.strip() in perms

def has_any_permission(user_role, permissions, required_list):
    """
    التحقق من وجود أي صلاحية من قائمة صلاحيات
    """
    if user_role == 'manager':
        return True
    perms = set(parse_permissions(permissions))
    return any(r in perms for r in required_list)

def has_permission(user_role, permissions, required):
    """
    التحقق من وجود صلاحية معينة للمستخدم (المدير دائمًا يملك كل الصلاحيات)
    """
    if user_role == 'manager':
        return True
    return required in parse_permissions(permissions)

def get_cogs_fifo(product_id, qty, db_path=None):
    """
    حساب تكلفة البضاعة المباعة (COGS) بطريقة FIFO لمنتج معين وكمية معينة.
    db_path: مسار قاعدة البيانات (افتراضي None لاستخدام database.DB_NAME)
    """
    from models import database
    db_file = db_path or database.DB_NAME
    conn = sqlite3.connect(db_file)
    c = conn.cursor()
    c.execute('''SELECT quantity, purchase_price FROM purchases WHERE product_id = ? ORDER BY purchase_date ASC, id ASC''', (product_id,))
    batches = c.fetchall()
    conn.close()
    needed = qty
    total_cost = 0.0
    for batch_qty, batch_price in batches:
        take = min(batch_qty, needed)
        total_cost += take * batch_price
        needed -= take
        if needed <= 0:
            break
    return total_cost

def get_profit_fifo(sales_df, db_path=None):
    """
    حساب الربح الفعلي لكل منتج في DataFrame مبيعات (يجب أن يحتوي على product_id, qty, sales)
    """
    profit = 0.0
    for _, row in sales_df.iterrows():
        cogs = get_cogs_fifo(row['product_id'], row['qty'], db_path)
        profit += row['sales'] - cogs
    return profit

def get_user_permissions(user_id, use_advanced=False):
    """
    جلب جميع الصلاحيات للمستخدم:
    - إذا use_advanced=True: من جداول user_permissions/role_permissions (إذا كانت موجودة)
    - إذا use_advanced=False (افتراضي): من عمود permissions فقط (الأبسط)
    """
    if not use_advanced:
        return get_user_permissions_from_db(user_id)
    # advanced: جلب من جداول user_permissions/role_permissions
    import sqlite3
    from models import database
    conn = sqlite3.connect(database.DB_NAME)
    c = conn.cursor()
    # صلاحيات مباشرة
    c.execute('''SELECT p.name FROM user_permissions up JOIN permissions p ON up.permission_id=p.id WHERE up.user_id=? AND up.allowed=1''', (user_id,))
    direct = set(row[0] for row in c.fetchall())
    # صلاحيات عبر الدور
    c.execute('SELECT role FROM users WHERE id=?', (user_id,))
    row = c.fetchone()
    role = row[0] if row else None
    role_perms = set()
    if role:
        c.execute('''SELECT p.name FROM role_permissions rp JOIN permissions p ON rp.permission_id=p.id WHERE rp.role=? AND rp.allowed=1''', (role,))
        role_perms = set(row[0] for row in c.fetchall())
    conn.close()
    return list(direct | role_perms)

# نظام منع تكرار النوافذ (نافذة واحدة فقط لكل نوع)
open_windows = {}

def show_singleton_window(window_class, *args, **kwargs):
    """
    فتح نافذة واحدة فقط من كل نوع (إذا كانت مفتوحة يتم تفعيلها فقط)
    """
    global open_windows
    if window_class in open_windows:
        win = open_windows[window_class]
        win.raise_()
        win.activateWindow()
        win.showNormal()
        return win
    win = window_class(*args, **kwargs)
    open_windows[window_class] = win
    win.show()
    def on_close(event=None):
        if window_class in open_windows:
            del open_windows[window_class]
    win.destroyed.connect(on_close)
    return win