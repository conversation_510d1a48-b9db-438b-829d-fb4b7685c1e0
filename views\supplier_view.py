# نافذة إدارة الموردين المتقدمة
# لإضافة وتعديل وحذف الموردين مع دعم الهاتف والعنوان والملاحظات
# تاريخ الإنشاء: 2025-05-09

from PyQt5 import QtWidgets, QtCore, QtGui
import sqlite3
from models import database
from utils.utilities import parse_permissions
from main import get_app_icon

class SupplierWindow(QtWidgets.QWidget):
    def __init__(self, permissions=None, labels=None):
        super().__init__()
        self.labels = labels or {
            'SUPPLIER_LABEL': 'مورد'
        }
        self.setWindowTitle(f"إدارة {self.labels['SUPPLIER_LABEL']}ين")
        self.setGeometry(450, 200, 700, 500)
        # حفظ الصلاحيات كقائمة
        self.permissions = parse_permissions(permissions)
        self.setup_ui()
        self.load_suppliers()

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout()
        form = QtWidgets.QFormLayout()
        # اسم المورد
        self.name_edit = QtWidgets.QLineEdit()
        form.addRow(f"اسم {self.labels['SUPPLIER_LABEL']}:", self.name_edit)
        # رقم الهاتف
        self.phone_edit = QtWidgets.QLineEdit()
        form.addRow('رقم الهاتف:', self.phone_edit)
        # العنوان
        self.address_edit = QtWidgets.QLineEdit()
        form.addRow('العنوان:', self.address_edit)
        # ملاحظات
        self.notes_edit = QtWidgets.QLineEdit()
        form.addRow('ملاحظات:', self.notes_edit)
        layout.addLayout(form)
        # أزرار
        btns = QtWidgets.QHBoxLayout()
        self.add_btn = QtWidgets.QPushButton(f"إضافة {self.labels['SUPPLIER_LABEL']}")
        self.add_btn.clicked.connect(self.add_supplier)
        btns.addWidget(self.add_btn)
        self.edit_btn = QtWidgets.QPushButton('تعديل')
        self.edit_btn.clicked.connect(self.edit_supplier)
        btns.addWidget(self.edit_btn)
        self.delete_btn = QtWidgets.QPushButton('حذف')
        self.delete_btn.clicked.connect(self.delete_supplier)
        btns.addWidget(self.delete_btn)
        
        # زر عرض تقارير الموردين
        self.report_btn = QtWidgets.QPushButton(f"عرض تقارير {self.labels['SUPPLIER_LABEL']}ين")
        self.report_btn.setStyleSheet('background-color: #455a64; color: white; font-weight: bold;')
        self.report_btn.clicked.connect(self.show_suppliers_report_window)
        btns.addWidget(self.report_btn)
        # زر طباعة تقرير الموردين
        self.print_btn = QtWidgets.QPushButton(f"طباعة {self.labels['SUPPLIER_LABEL']}ين")
        self.print_btn.setStyleSheet('background-color: #4caf50; color: white; font-weight: bold;')
        self.print_btn.clicked.connect(self.print_current_suppliers)
        btns.addWidget(self.print_btn)
        # الأزرار تعمل دائماً بغض النظر عن الصلاحيات
        layout.addLayout(btns)
        # جدول الموردين
        self.table = QtWidgets.QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['الاسم', 'الهاتف', 'العنوان', 'ملاحظات', ''])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.table.cellClicked.connect(self.fill_form_from_table)
        layout.addWidget(self.table)
        self.status_label = QtWidgets.QLabel('')
        layout.addWidget(self.status_label)
        self.setLayout(layout)
        self.setWindowIcon(get_app_icon())

    def load_suppliers(self):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name, phone, address, notes FROM suppliers ORDER BY name')
        rows = c.fetchall()
        conn.close()
        self.table.setRowCount(len(rows))
        for i, row in enumerate(rows):
            for j, val in enumerate(row):
                self.table.setItem(i, j, QtWidgets.QTableWidgetItem(str(val)))
            # زر حذف سريع
            del_btn = QtWidgets.QPushButton('حذف')
            del_btn.clicked.connect(lambda _, r=i: self.delete_supplier_row(r))
            self.table.setCellWidget(i, 4, del_btn)

    def add_supplier(self):
        name = self.name_edit.text().strip()
        phone = self.phone_edit.text().strip()
        address = self.address_edit.text().strip()
        notes = self.notes_edit.text().strip()
        if not name:
            self.status_label.setText('يرجى إدخال اسم المورد')
            self.status_label.setStyleSheet('color: red')
            return
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        # تحقق من عدم وجود اسم مورد مكرر
        c.execute('SELECT COUNT(*) FROM suppliers WHERE name = ?', (name,))
        if c.fetchone()[0] > 0:
            self.status_label.setText('اسم المورد مستخدم مسبقًا')
            self.status_label.setStyleSheet('color: red')
            conn.close()
            return
        try:
            c.execute('INSERT INTO suppliers (name, phone, address, notes) VALUES (?, ?, ?, ?)',
                      (name, phone, address, notes))
            conn.commit()
            self.status_label.setText('تمت إضافة المورد بنجاح')
            self.status_label.setStyleSheet('color: green')
            self.load_suppliers()
        except sqlite3.IntegrityError:
            self.status_label.setText('اسم المورد مستخدم مسبقًا')
            self.status_label.setStyleSheet('color: red')
        finally:
            conn.close()
        self.clear_form()

    def edit_supplier(self):
        row = self.table.currentRow()
        if row < 0:
            self.status_label.setText('يرجى اختيار مورد للتعديل')
            self.status_label.setStyleSheet('color: red')
            return
        name = self.name_edit.text().strip()
        phone = self.phone_edit.text().strip()
        address = self.address_edit.text().strip()
        notes = self.notes_edit.text().strip()
        old_name = self.table.item(row, 0).text()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('UPDATE suppliers SET name=?, phone=?, address=?, notes=? WHERE name=?',
                  (name, phone, address, notes, old_name))
        conn.commit()
        conn.close()
        self.status_label.setText('تم تعديل المورد')
        self.status_label.setStyleSheet('color: green')
        self.load_suppliers()
        self.clear_form()

    def delete_supplier(self):
        row = self.table.currentRow()
        if row < 0:
            self.status_label.setText('يرجى اختيار مورد للحذف')
            self.status_label.setStyleSheet('color: red')
            return
        name = self.table.item(row, 0).text()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('DELETE FROM suppliers WHERE name=?', (name,))
        conn.commit()
        conn.close()
        self.status_label.setText('تم حذف المورد')
        self.status_label.setStyleSheet('color: green')
        self.load_suppliers()
        self.clear_form()

    def delete_supplier_row(self, row):
        name = self.table.item(row, 0).text()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('DELETE FROM suppliers WHERE name=?', (name,))
        conn.commit()
        conn.close()
        self.status_label.setText('تم حذف المورد')
        self.status_label.setStyleSheet('color: green')
        self.load_suppliers()
        self.clear_form()

    def fill_form_from_table(self, row, col):
        self.name_edit.setText(self.table.item(row, 0).text())
        self.phone_edit.setText(self.table.item(row, 1).text())
        self.address_edit.setText(self.table.item(row, 2).text())
        self.notes_edit.setText(self.table.item(row, 3).text())

    def clear_form(self):
        self.name_edit.clear()
        self.phone_edit.clear()
        self.address_edit.clear()
        self.notes_edit.clear()

    def show_suppliers_report_window(self):
        """
        عرض نافذة تقارير التغييرات في الموردين.
        """
        report_window = QtWidgets.QDialog(self)
        report_window.setWindowTitle('تقارير التغييرات - الموردين')
        report_window.setGeometry(500, 250, 700, 400)
        layout = QtWidgets.QVBoxLayout()
        label = QtWidgets.QLabel('سجل التغييرات في الموردين:')
        label.setStyleSheet('font-size: 14pt; font-weight: bold; color: #263238;')
        layout.addWidget(label)
        table = QtWidgets.QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(['العملية', 'اسم المورد', 'الكمية', 'المستخدم', 'التاريخ'])
        table.horizontalHeader().setStretchLastSection(True)
        # جلب البيانات من جدول log إذا كان موجوداً
        try:
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('''SELECT action, supplier_name, quantity, user, date FROM log WHERE section = ? ORDER BY date DESC LIMIT 50''', ('suppliers',))
            rows = c.fetchall()
            conn.close()
            table.setRowCount(len(rows))
            for row_idx, row_data in enumerate(rows):
                for col_idx, value in enumerate(row_data):
                    table.setItem(row_idx, col_idx, QtWidgets.QTableWidgetItem(str(value)))
        except Exception as e:
            table.setRowCount(1)
            table.setItem(0, 0, QtWidgets.QTableWidgetItem('لا يوجد سجل أو قاعدة بيانات التقارير غير مفعلة'))
        layout.addWidget(table)
        close_btn = QtWidgets.QPushButton('إغلاق')
        close_btn.clicked.connect(report_window.close)
        layout.addWidget(close_btn)
        report_window.setLayout(layout)
        report_window.exec_()

    def print_current_suppliers(self):
        from utils.print_utils import print_invoice
        html = self.generate_suppliers_html()
        print_invoice(html, self)

    def generate_suppliers_html(self):
        items_html = ""
        for row in self.get_suppliers_rows():
            items_html += f"<tr>{''.join(f'<td>{cell}</td>' for cell in row)}</tr>"
        html = f"""
        <html><head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}}</style></head><body dir='rtl'>
        <h2 style='text-align:center'>تقرير الموردين</h2>
        <table>{items_html}</table></body></html>"""
        return html

    def get_suppliers_rows(self):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name, phone, address, notes FROM suppliers ORDER BY name')
        rows = c.fetchall()
        conn.close()
        return rows
