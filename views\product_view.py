# نافذة إدارة المنتجات المتقدمة
# لإضافة وتعديل وحذف المنتجات مع دعم التصنيفات والباركود ووحدة القياس
# تاريخ الإنشاء: 2025-05-09

from PyQt5 import QtWidgets, QtCore, QtGui
import sqlite3
from models import database
from utils.utilities import parse_permissions
from main import get_app_icon

class ProductWindow(QtWidgets.QWidget):
    def __init__(self, permissions=None, labels=None):
        super().__init__()
        self.labels = labels or {
            'PRODUCT_LABEL': 'منتج',
            'UNIT_OPTIONS': ['قطعة', 'كيلو', 'كرتون', 'علبة', 'لتر', 'باكت']
        }
        self.setWindowTitle(f"إدارة {self.labels['PRODUCT_LABEL']}ات")
        self.setGeometry(450, 200, 700, 500)
        self.setWindowIcon(get_app_icon())
        if permissions is None:
            self.permissions = []
        elif isinstance(permissions, str):
            self.permissions = permissions.split(',')
        else:
            self.permissions = permissions
        self.setup_ui()
        self.refresh_unit_options()  # تحديث وحدات القياس بعد تهيئة الواجهة
        self.load_products()

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout()
        form = QtWidgets.QFormLayout()
        # اسم المنتج
        self.name_edit = QtWidgets.QLineEdit()
        form.addRow(f"اسم {self.labels['PRODUCT_LABEL']}:", self.name_edit)
        # التصنيف
        self.category_edit = QtWidgets.QLineEdit()
        form.addRow('التصنيف:', self.category_edit)
        # نوع المنتج
        self.type_edit = QtWidgets.QLineEdit()
        form.addRow(f"نوع {self.labels['PRODUCT_LABEL']}:", self.type_edit)
        # الباركود
        self.barcode_edit = QtWidgets.QLineEdit()
        form.addRow('الباركود:', self.barcode_edit)
        # حذف وحدة القياس القديمة وكتابتها من جديد
        self.unit_combo = QtWidgets.QComboBox()
        unit_options = self.labels.get('UNIT_OPTIONS', ['قطعة', 'كيلو', 'كرتون', 'علبة', 'لتر', 'باكت'])
        self.unit_combo.clear()
        self.unit_combo.addItems(unit_options)
        form.addRow('وحدة القياس:', self.unit_combo)
        self.unit_edit = None
        # الحد الأدنى للكميةاريد اضافة زر بعد التعديل سعر البيع من الجدول  عند الضغط على زر التعديل  يتم حفض التعديل ولا يكون يحتفض تلقائيا
        self.min_qty_spin = QtWidgets.QSpinBox()
        self.min_qty_spin.setRange(0, 100000)
        form.addRow('الحد الأدنى:', self.min_qty_spin)
        # تاريخ الانتهاء (اختياري)
        self.expiry_date_edit = QtWidgets.QDateEdit(QtCore.QDate.currentDate())
        self.expiry_date_edit.setCalendarPopup(True)
        self.expiry_date_edit.setDisplayFormat('yyyy-MM-dd')
        self.expiry_date_edit.setSpecialValueText('بدون')
        self.expiry_date_edit.setDate(QtCore.QDate(2000, 1, 1))
        form.addRow('تاريخ الانتهاء (اختياري):', self.expiry_date_edit)
        layout.addLayout(form)
        # أزرار
        btns = QtWidgets.QHBoxLayout()
        self.add_btn = QtWidgets.QPushButton(f"إضافة {self.labels['PRODUCT_LABEL']}")
        self.add_btn.clicked.connect(self.add_product)
        btns.addWidget(self.add_btn)
        self.edit_btn = QtWidgets.QPushButton('تعديل')
        self.edit_btn.clicked.connect(self.edit_product)
        btns.addWidget(self.edit_btn)
        self.delete_btn = QtWidgets.QPushButton('حذف')
        self.delete_btn.clicked.connect(self.delete_product)
        btns.addWidget(self.delete_btn)
        
        # زر عرض تقارير المنتجات
        self.report_btn = QtWidgets.QPushButton(f"عرض تقارير {self.labels['PRODUCT_LABEL']}ات")
        self.report_btn.setStyleSheet('background-color: #455a64; color: white; font-weight: bold;')
        self.report_btn.clicked.connect(self.show_products_report_window)
        btns.addWidget(self.report_btn)

        # زر طباعة تقرير المنتجات
        print_btn = QtWidgets.QPushButton(f"طباعة {self.labels['PRODUCT_LABEL']}ات")
        print_btn.clicked.connect(self.print_current_products)
        btns.addWidget(print_btn)

        layout.addLayout(btns)
        # جدول المنتجات
        self.table = QtWidgets.QTableWidget()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels(['الاسم', 'التصنيف', 'نوع المنتج', 'الباركود', 'الوحدة', 'الحد الأدنى', ''])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.table.cellClicked.connect(self.fill_form_from_table)
        layout.addWidget(self.table)
        self.status_label = QtWidgets.QLabel('')
        layout.addWidget(self.status_label)
        self.setLayout(layout)

    def load_products(self):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name, category, type, barcode, unit, min_quantity FROM products ORDER BY name')
        rows = c.fetchall()
        conn.close()
        self.table.setRowCount(len(rows))
        for i, row in enumerate(rows):
            for j, val in enumerate(row):
                self.table.setItem(i, j, QtWidgets.QTableWidgetItem(str(val)))
            # زر حذف سريع
            del_btn = QtWidgets.QPushButton('حذف')
            del_btn.clicked.connect(lambda _, r=i: self.delete_product_row(r))
            self.table.setCellWidget(i, 6, del_btn)

    def add_product(self):
        name = self.name_edit.text().strip()
        category = self.category_edit.text().strip()
        product_type = self.type_edit.text().strip()
        barcode = self.barcode_edit.text().strip()
        unit = self.unit_combo.currentText()
        min_qty = self.min_qty_spin.value()
        expiry_date = self.expiry_date_edit.date().toString('yyyy-MM-dd')
        print(f"[DEBUG] وحدة القياس المختارة: {unit}")  # تتبع القيمة المختارة
        if not name:
            self.status_label.setText('يرجى إدخال اسم المنتج')
            self.status_label.setStyleSheet('color: red')
            return
        if not unit:
            self.status_label.setText('يرجى اختيار وحدة القياس')
            self.status_label.setStyleSheet('color: red')
            return
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        try:
            c.execute('INSERT INTO products (name, category, type, barcode, unit, min_quantity) VALUES (?, ?, ?, ?, ?, ?)',
                      (name, category, product_type, barcode, unit, min_qty))
            product_id = c.lastrowid
            # إضافة المنتج للمخزون مباشرة عند إنشائه
            c.execute('INSERT INTO inventory (product_id, quantity, expiry_date) VALUES (?, ?, ?)', (product_id, 0, ''))
            conn.commit()
            self.status_label.setText('تمت إضافة المنتج بنجاح')
            self.load_products()
            # تحديث قوائم المنتجات في جميع نوافذ المشتريات والمبيعات
            for widget in QtWidgets.QApplication.topLevelWidgets():
                if widget.__class__.__name__ in ['PurchaseWindow', 'SalesWindow']:
                    if hasattr(widget, 'refresh_products'):
                        widget.refresh_products()
        except sqlite3.IntegrityError:
            self.status_label.setText('اسم المنتج أو الباركود مستخدم مسبقًا')
            self.status_label.setStyleSheet('color: red')
        finally:
            conn.close()
        self.clear_form()

    def edit_product(self):
        row = self.table.currentRow()
        if row < 0:
            return
        name = self.name_edit.text().strip()
        category = self.category_edit.text().strip()
        product_type = self.type_edit.text().strip()
        barcode = self.barcode_edit.text().strip()
        unit = self.unit_combo.currentText()
        min_qty = self.min_qty_spin.value()
        old_name = self.table.item(row, 0).text()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('UPDATE products SET name=?, category=?, type=?, barcode=?, unit=?, min_quantity=? WHERE name=?',
                  (name, category, product_type, barcode, unit, min_qty, old_name))
        conn.commit()
        conn.close()
        self.status_label.setText('تم تعديل المنتج')
        self.status_label.setStyleSheet('color: green')
        self.load_products()
        # تحديث قوائم المنتجات في جميع نوافذ المشتريات والمبيعات
        for widget in QtWidgets.QApplication.topLevelWidgets():
            if widget.__class__.__name__ in ['PurchaseWindow', 'SalesWindow']:
                if hasattr(widget, 'refresh_products'):
                    widget.refresh_products()
        self.clear_form()

    def delete_product(self):
        row = self.table.currentRow()
        if row < 0:
            self.status_label.setText('يرجى اختيار منتج للحذف')
            self.status_label.setStyleSheet('color: red')
            return
        name = self.table.item(row, 0).text()
        # تحقق من عدم وجود عمليات شراء أو مبيعات مرتبطة بالمنتج
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*) FROM purchases WHERE product_id = (SELECT id FROM products WHERE name=?)', (name,))
        purchase_count = c.fetchone()[0]
        c.execute('SELECT COUNT(*) FROM sales_items WHERE product_id = (SELECT id FROM products WHERE name=?)', (name,))
        sales_count = c.fetchone()[0]
        if purchase_count > 0 or sales_count > 0:
            self.status_label.setText('لا يمكن حذف المنتج لوجود عمليات شراء أو مبيعات مرتبطة به')
            self.status_label.setStyleSheet('color: red; font-weight: bold;')
            conn.close()
            return
        c.execute('DELETE FROM products WHERE name=?', (name,))
        conn.commit()
        conn.close()
        self.status_label.setText('تم حذف المنتج')
        self.status_label.setStyleSheet('color: green')
        self.load_products()
        # تحديث قوائم المنتجات في جميع نوافذ المشتريات والمبيعات
        for widget in QtWidgets.QApplication.topLevelWidgets():
            if widget.__class__.__name__ in ['PurchaseWindow', 'SalesWindow']:
                if hasattr(widget, 'refresh_products'):
                    widget.refresh_products()
        self.clear_form()

    def delete_product_row(self, row):
        name = self.table.item(row, 0).text()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('DELETE FROM products WHERE name=?', (name,))
        conn.commit()
        conn.close()
        self.status_label.setText('تم حذف المنتج')
        self.status_label.setStyleSheet('color: green')
        self.load_products()
        self.clear_form()

    def fill_form_from_table(self, row, col):
        self.name_edit.setText(self.table.item(row, 0).text())
        self.category_edit.setText(self.table.item(row, 1).text())
        self.type_edit.setText(self.table.item(row, 2).text())
        self.barcode_edit.setText(self.table.item(row, 3).text())
        unit_value = self.table.item(row, 4).text()
        self.unit_combo.setCurrentText(unit_value)
        self.min_qty_spin.setValue(int(self.table.item(row, 5).text()))

    def clear_form(self):
        self.name_edit.clear()
        self.category_edit.clear()
        self.type_edit.clear()
        self.barcode_edit.clear()
        if self.unit_combo:
            self.unit_combo.setCurrentIndex(0)
        self.min_qty_spin.setValue(0)
        self.expiry_date_edit.setDate(QtCore.QDate(2000, 1, 1))

    def show_products_report_window(self):
        """
        عرض نافذة تقارير التغييرات في المنتجات.
        """
        report_window = QtWidgets.QDialog(self)
        report_window.setWindowTitle('تقارير التغييرات - المنتجات')
        report_window.setGeometry(500, 250, 700, 400)
        layout = QtWidgets.QVBoxLayout()
        label = QtWidgets.QLabel('سجل التغييرات في المنتجات:')
        label.setStyleSheet('font-size: 14pt; font-weight: bold; color: #263238;')
        layout.addWidget(label)
        table = QtWidgets.QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(['العملية', 'اسم المنتج', 'الكمية', 'المستخدم', 'التاريخ'])
        table.horizontalHeader().setStretchLastSection(True)
        # جلب البيانات من جدول log إذا كان موجوداً
        try:
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('''SELECT action, product_name, quantity, user, date FROM log WHERE section = ? ORDER BY date DESC LIMIT 50''', ('products',))
            rows = c.fetchall()
            conn.close()
            table.setRowCount(len(rows))
            for row_idx, row_data in enumerate(rows):
                for col_idx, value in enumerate(row_data):
                    table.setItem(row_idx, col_idx, QtWidgets.QTableWidgetItem(str(value)))
        except Exception as e:
            table.setRowCount(1)
            table.setItem(0, 0, QtWidgets.QTableWidgetItem('لا يوجد سجل أو قاعدة بيانات التقارير غير مفعلة'))
        layout.addWidget(table)
        close_btn = QtWidgets.QPushButton('إغلاق')
        close_btn.clicked.connect(report_window.close)
        layout.addWidget(close_btn)
        report_window.setLayout(layout)
        report_window.exec_()

    def print_current_products(self):
        from utils.print_utils import print_invoice
        html = self.generate_products_html()
        print_invoice(html, self)

    def generate_products_html(self):
        items_html = ""
        for row in self.get_products_rows():
            items_html += f"<tr>{''.join(f'<td>{cell}</td>' for cell in row)}</tr>"
        html = f"""
        <html><head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}}</style></head><body dir='rtl'>
        <h2 style='text-align:center'>تقرير المنتجات</h2>
        <table>{items_html}</table></body></html>"""
        return html

    def get_products_rows(self):
        rows = []
        for row in range(self.table.rowCount()):
            row_data = []
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                row_data.append(item.text() if item is not None else '')
            rows.append(row_data)
        return rows

    def refresh_unit_options(self):
        """
        تحديث وحدات القياس في ComboBox بناءً على labels['UNIT_OPTIONS']
        """
        self.unit_combo.clear()
        self.unit_combo.addItems(self.labels.get('UNIT_OPTIONS', ['قطعة', 'كيلو', 'كرتون', 'علبة', 'لتر', 'باكت']))

    def update_labels(self, labels):
        """
        تحديث المسميات ووحدات القياس ديناميكياً عند تغيير النشاط
        """
        self.labels = labels
        self.setWindowTitle(f"إدارة {self.labels['PRODUCT_LABEL']}ات")
        self.refresh_unit_options()
        # تحديث النصوص الأخرى إذا لزم الأمر
        self.add_btn.setText(f"إضافة {self.labels['PRODUCT_LABEL']}")
        self.report_btn.setText(f"عرض تقارير {self.labels['PRODUCT_LABEL']}ات")
        # ...يمكن تحديث بقية النصوص إذا لزم الأمر...
