"""
activation_core.py
------------------
نظام التفعيل الزمني وتسجيل المالك للبرنامج (آمن ومشفر)
"""
import os
import json
import base64
import hashlib
from cryptography.fernet import Fernet
from datetime import datetime

_SECRET_HASH = 'b6e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2e2'  # (غير مستخدم مباشرة)

# مسارات الملفات
ACTIVATION_FILE = 'activation.dat'
HISTORY_FILE = 'activation_history.log'

# مفتاح تشفير مشتق من الرمز السري (غير ظاهر للمستخدم)
def _get_fernet_key():
    # استخدم قيمة ثابتة مشتقة من الرمز السري (غير نص الرمز نفسه)
    key_material = 'azamfahd775715333'.encode('utf-8')
    key = hashlib.sha256(key_material).digest()
    return base64.urlsafe_b64encode(key)

fernet = Fernet(_get_fernet_key())

def check_owner_code(code: str) -> bool:
    """
    تحقق من صحة الرمز السري للمالك (مقارنة هاش وليس نص صريح)
    """
    code_hash = hashlib.sha256(code.encode('utf-8')).hexdigest()
    # الرمز الصحيح هو azamfahd775715333
    return code_hash == '573d39babf5794a9cb2f7a2e6ef71ba8c4f0d35c7f3e91c49922e0fa92dc6d54'

def save_activation_period(start_date: str, end_date: str):
    """
    حفظ فترة التفعيل بشكل مشفر + إضافة للسجل التاريخي
    """
    data = {'start': start_date, 'end': end_date}
    enc = fernet.encrypt(json.dumps(data).encode('utf-8'))
    with open(ACTIVATION_FILE, 'wb') as f:
        f.write(enc)
    # سجل تاريخي
    with open(HISTORY_FILE, 'a', encoding='utf-8') as log:
        log.write(f"{datetime.now().isoformat()} | {start_date} -> {end_date}\n")

def load_activation_period():
    """
    قراءة فترة التفعيل (ترجع None إذا لم يوجد أو خطأ)
    """
    if not os.path.exists(ACTIVATION_FILE):
        return None
    try:
        with open(ACTIVATION_FILE, 'rb') as f:
            enc = f.read()
        data = json.loads(fernet.decrypt(enc).decode('utf-8'))
        return data['start'], data['end']
    except Exception:
        return None

def is_period_valid() -> bool:
    """
    تحقق هل الفترة الحالية ضمن فترة التفعيل
    """
    period = load_activation_period()
    if not period:
        return False
    start, end = period
    now = datetime.now().date()
    def parse_date(s):
        # يدعم 2025/07/05 أو 2025/7/5
        parts = s.split('/')
        if len(parts) == 3:
            y, m, d = parts
            return datetime(int(y), int(m), int(d)).date()
        raise ValueError('تنسيق تاريخ غير مدعوم')
    try:
        start_dt = parse_date(start)
        end_dt = parse_date(end)
        return start_dt <= now <= end_dt
    except Exception:
        return False
