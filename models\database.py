import sqlite3

# اسم ملف قاعدة البيانات
DB_NAME = 'restaurant.db'

# إنشاء مستخدم مدير افتراضي إذا لم يكن موجوداً
# اسم المستخدم: admin وكلمة المرور: admin (مشفر SHA256)
def create_default_admin():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute("SELECT COUNT(*) FROM users WHERE username = ?", ('admin',))
    if c.fetchone()[0] == 0:
        import hashlib
        password_hash = hashlib.sha256('admin'.encode()).hexdigest()
        c.execute("INSERT INTO users (username, password, role, permissions) VALUES (?, ?, ?, ?)",
                  ('admin', password_hash, 'manager', ''))
        conn.commit()
    conn.close()

# إصلاح جدول المبيعات بإضافة عمود payment_method إذا لم يكن موجوداً
def fix_sales_table():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute("PRAGMA table_info(sales)")
    columns = [row[1] for row in c.fetchall()]
    if 'payment_method' not in columns:
        try:
            c.execute("ALTER TABLE sales ADD COLUMN payment_method TEXT")
            conn.commit()
        except Exception as e:
            print('خطأ في إصلاح جدول المبيعات:', e)
    conn.close()

# إصلاح جدول المنتجات بإضافة أعمدة barcode وunit وtype وmarket_price إذا لم تكن موجودة
def fix_products_table():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute("PRAGMA table_info(products)")
    columns = [row[1] for row in c.fetchall()]
    if 'barcode' not in columns:
        c.execute("ALTER TABLE products ADD COLUMN barcode TEXT")
    if 'unit' not in columns:
        c.execute("ALTER TABLE products ADD COLUMN unit TEXT")
    if 'type' not in columns:
        c.execute("ALTER TABLE products ADD COLUMN type TEXT")
    # إضافة عمود market_price إذا لم يكن موجوداً
    if 'market_price' not in columns:
        c.execute("ALTER TABLE products ADD COLUMN market_price REAL")
    conn.commit()
    conn.close()

# إصلاح جدول الموردين بإضافة أعمدة phone وaddress وnotes إذا لم تكن موجودة
def fix_suppliers_table():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute("PRAGMA table_info(suppliers)")
    columns = [row[1] for row in c.fetchall()]
    if 'phone' not in columns:
        c.execute("ALTER TABLE suppliers ADD COLUMN phone TEXT")
    if 'address' not in columns:
        c.execute("ALTER TABLE suppliers ADD COLUMN address TEXT")
    if 'notes' not in columns:
        c.execute("ALTER TABLE suppliers ADD COLUMN notes TEXT")
    conn.commit()
    conn.close()

# إصلاح جدول العملاء بإضافة أعمدة phone وbalance وnotes إذا لم تكن موجودة
def fix_customers_table():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute("PRAGMA table_info(customers)")
    columns = [row[1] for row in c.fetchall()]
    if 'phone' not in columns:
        c.execute("ALTER TABLE customers ADD COLUMN phone TEXT")
    if 'balance' not in columns:
        c.execute("ALTER TABLE customers ADD COLUMN balance REAL DEFAULT 0")
    if 'notes' not in columns:
        c.execute("ALTER TABLE customers ADD COLUMN notes TEXT")
    # إضافة عمود الرصيد السالب/المستحق إذا لم يكن موجودًا
    if 'due_amount' not in columns:
        c.execute("ALTER TABLE customers ADD COLUMN due_amount REAL DEFAULT 0")
    conn.commit()
    conn.close()

# إصلاح جدول المستخدمين بإضافة عمود permissions إذا لم يكن موجوداً
def fix_users_table():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute("PRAGMA table_info(users)")
    columns = [row[1] for row in c.fetchall()]
    if 'permissions' not in columns:
        c.execute("ALTER TABLE users ADD COLUMN permissions TEXT DEFAULT ''")
    conn.commit()
    conn.close()

# إصلاح جدول المخزون بإضافة عمود expiry_date إذا لم يكن موجوداً
def fix_inventory_table():
    """
    إصلاح جدول المخزون بإضافة عمود expiry_date إذا لم يكن موجوداً.
    """
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    # التحقق من وجود العمود
    c.execute("PRAGMA table_info(inventory)")
    columns = [row[1] for row in c.fetchall()]
    if 'expiry_date' not in columns:
        c.execute("ALTER TABLE inventory ADD COLUMN expiry_date TEXT")
        conn.commit()
    conn.close()

# إصلاح جدول الدفعات إذا لم يكن موجوداً
def fix_batches_table():
    """
    إصلاح جدول الدفعات إذا لم يكن موجوداً.
    """
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    c.execute("""CREATE TABLE IF NOT EXISTS batches (
        batch_id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER,
        quantity INTEGER,
        expiry_date TEXT,
        purchase_date TEXT,
        batch_number TEXT,
        FOREIGN KEY(product_id) REFERENCES products(id)
    )""")
    conn.commit()
    conn.close()

# تهيئة قاعدة البيانات وإنشاء جميع الجداول الأساسية إذا لم تكن موجودة
def init_db():
    conn = sqlite3.connect(DB_NAME)
    c = conn.cursor()
    # جدول المستخدمين
    c.execute('''CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE,
        password TEXT,
        role TEXT,
        permissions TEXT DEFAULT ''
    )''')
    # جدول الموردين
    c.execute('''CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        phone TEXT,
        address TEXT,
        notes TEXT
    )''')
    # جدول المنتجات
    c.execute('''CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        category TEXT,
        min_quantity INTEGER DEFAULT 0,
        barcode TEXT,
        unit TEXT,
        type TEXT,
        market_price REAL
    )''')
    # جدول المشتريات
    c.execute('''CREATE TABLE IF NOT EXISTS purchases (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER,
        supplier_id INTEGER,
        quantity INTEGER,
        purchase_price REAL,
        sale_price REAL,
        purchase_date TEXT,
        FOREIGN KEY(product_id) REFERENCES products(id),
        FOREIGN KEY(supplier_id) REFERENCES suppliers(id)
    )''')
    # إضافة عمود sale_price إذا لم يكن موجوداً (ترقية قاعدة البيانات)
    try:
        c.execute('ALTER TABLE purchases ADD COLUMN sale_price REAL')
    except Exception:
        pass  # العمود موجود بالفعل
    # جدول المبيعات
    c.execute('''CREATE TABLE IF NOT EXISTS sales (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_name TEXT,
        sale_date TEXT,
        total REAL,
        payment_method TEXT
    )''')
    # جدول عناصر المبيعات
    c.execute('''CREATE TABLE IF NOT EXISTS sales_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_id INTEGER,
        product_id INTEGER,
        quantity INTEGER,
        sale_price REAL,
        discount REAL DEFAULT 0,
        FOREIGN KEY(sale_id) REFERENCES sales(id),
        FOREIGN KEY(product_id) REFERENCES products(id)
    )''')
    # جدول المخزون
    c.execute('''CREATE TABLE IF NOT EXISTS inventory (
        product_id INTEGER PRIMARY KEY,
        quantity INTEGER,
        expiry_date TEXT,
        FOREIGN KEY(product_id) REFERENCES products(id)
    )''')
    # جدول الخصومات
    c.execute('''CREATE TABLE IF NOT EXISTS discounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER,
        discount_type TEXT,
        value REAL,
        start_date TEXT,
        end_date TEXT,
        FOREIGN KEY(product_id) REFERENCES products(id)
    )''')
    # جدول مرتجعات المبيعات
    c.execute('''CREATE TABLE IF NOT EXISTS sales_returns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_id INTEGER,
        product_id INTEGER,
        quantity INTEGER,
        return_date TEXT,
        reason TEXT,
        FOREIGN KEY(sale_id) REFERENCES sales(id),
        FOREIGN KEY(product_id) REFERENCES products(id)
    )''')
    # جدول العملاء
    c.execute('''CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        phone TEXT,
        balance REAL DEFAULT 0,
        notes TEXT,
        due_amount REAL DEFAULT 0
    )''')
    # جدول الدفعات (Batch/Lot)
    c.execute('''CREATE TABLE IF NOT EXISTS batches (
        batch_id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER,
        quantity INTEGER,
        expiry_date TEXT,
        purchase_date TEXT,
        batch_number TEXT,
        FOREIGN KEY(product_id) REFERENCES products(id)
    )''')
    # جدول الصلاحيات
    c.execute('''CREATE TABLE IF NOT EXISTS permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE,
        description TEXT
    )''')
    # جدول ربط المستخدمين بالصلاحيات (لصلاحيات مخصصة لكل مستخدم)
    c.execute('''CREATE TABLE IF NOT EXISTS user_permissions (
        user_id INTEGER,
        permission_id INTEGER,
        allowed INTEGER DEFAULT 1,
        FOREIGN KEY(user_id) REFERENCES users(id),
        FOREIGN KEY(permission_id) REFERENCES permissions(id),
        PRIMARY KEY(user_id, permission_id)
    )''')
    # جدول ربط الأدوار بالصلاحيات (لصلاحيات حسب الدور)
    c.execute('''CREATE TABLE IF NOT EXISTS role_permissions (
        role TEXT,
        permission_id INTEGER,
        allowed INTEGER DEFAULT 1,
        FOREIGN KEY(permission_id) REFERENCES permissions(id),
        PRIMARY KEY(role, permission_id)
    )''')
    # جدول تدقيق الأحداث (Audit Log)
    c.execute('''CREATE TABLE IF NOT EXISTS audit_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action TEXT,
        timestamp TEXT,
        status TEXT,
        details TEXT,
        FOREIGN KEY(user_id) REFERENCES users(id)
    )''')
    conn.commit()
    conn.close()
    create_default_admin()  # إنشاء مدير افتراضي
    fix_sales_table()      # إصلاح جدول المبيعات إذا لزم الأمر
    fix_products_table()   # إصلاح جدول المنتجات إذا لزم الأمر
    fix_suppliers_table()  # إصلاح جدول الموردين إذا لزم الأمر
    fix_customers_table()  # إصلاح جدول العملاء إذا لزم الأمر
    fix_users_table()      # إصلاح جدول المستخدمين إذا لزم الأمر
    fix_inventory_table()  # إصلاح جدول المخزون إذا لزم الأمر
    fix_batches_table()    # إصلاح جدول الدفعات إذا لزم الأمر
