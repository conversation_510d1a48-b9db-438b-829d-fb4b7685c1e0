('C:\\appazam\\resterant2\\build\\main\\main.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'C:\\appazam\\resterant2\\build\\main\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'C:\\appazam\\resterant2\\build\\main\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\appazam\\resterant2\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\appazam\\resterant2\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\appazam\\resterant2\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\appazam\\resterant2\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main', 'C:\\appazam\\resterant2\\main.py', 'PYSOURCE'),
  ('sklearn\\.libs\\msvcp140.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\.libs\\msvcp140.dll',
   'BINARY'),
  ('sklearn\\.libs\\vcomp140.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\.libs\\vcomp140.dll',
   'BINARY'),
  ('python313.dll', 'C:\\Program Files\\Python313\\python313.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('_lzma.pyd', 'C:\\Program Files\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('PyQt5\\sip.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_cython_blas.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\kiwisolver\\_cext.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\_tri.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\contourpy\\_contourpy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\ft2font.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_mvn.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\stats\\_mvn.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cython_nnls.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_cython_nnls.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqp.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_slsqp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cobyla.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_cobyla.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\special\\_comb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_bspl.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_bspl.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\index.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\json.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\join.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_isfinite.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_isfinite.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\sparsefuncs_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\murmurhash.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\murmurhash.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_typedefs.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_typedefs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_vector_sentinel.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_sorting.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_sorting.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_heap.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_heap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_random.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_random.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\preprocessing\\_target_encoder_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_dist_metrics.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_weight_vector.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_openmp_helpers.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\__check_build\\_check_build.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_partition_nodes.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_criterion.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_criterion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_partitioner.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_partitioner.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_utils.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_tree.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_splitter.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_splitter.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_quad_tree.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_sgd_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_loss\\_loss.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\_loss\\_loss.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_sag_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_libsvm_sparse.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_libsvm.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_liblinear.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_liblinear.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\arrayfuncs.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_cd_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_seq_dataset.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\decomposition\\_cdnmf_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\decomposition\\_online_lda_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_kd_tree.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_ball_tree.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\manifold\\_utils.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\manifold\\_barnes_hut_tsne.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_isotonic.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\_isotonic.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_minibatch.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_lloyd.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_elkan.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_common.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_tree.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_dbscan_inner.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_optimal_leaf_ordering.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\cluster\\_optimal_leaf_ordering.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_hierarchy.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\cluster\\_hierarchy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_vq.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\cluster\\_vq.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_fast_dict.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hierarchical_fast.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('bidi\\bidi.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\bidi\\bidi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fontTools\\varLib\\iup.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\fontTools\\varLib\\iup.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fontTools\\misc\\bezierTools.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\fontTools\\misc\\bezierTools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\robust\\_qn.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\robust\\_qn.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\nonparametric\\linbin.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\nonparametric\\linbin.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\nonparametric\\_smoothers_lowess.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\nonparametric\\_smoothers_lowess.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_sosfilt.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\signal\\_sosfilt.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_upfirdn_apply.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\signal\\_upfirdn_apply.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_sigtools.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\signal\\_sigtools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_peak_finding_utils.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\signal\\_peak_finding_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_spline.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\signal\\_spline.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_max_len_seq_inner.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\signal\\_max_len_seq_inner.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fftpack\\convolve.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\fftpack\\convolve.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\stl\\_stl.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\stl\\_stl.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\regime_switching\\_kim_smoother.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\regime_switching\\_kim_smoother.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\regime_switching\\_hamilton_filter.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\regime_switching\\_hamilton_filter.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\innovations\\_arma_innovations.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\innovations\\_arma_innovations.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\holtwinters\\_exponential_smoothers.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\holtwinters\\_exponential_smoothers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\exponential_smoothing\\_ets_smooth.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\exponential_smoothing\\_ets_smooth.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\_innovations.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\_innovations.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_tools.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_tools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_cfa_simulation_smoother.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_cfa_simulation_smoother.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_simulation_smoother.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_simulation_smoother.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_kalman_smoother.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_kalman_smoother.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_kalman_filter.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_kalman_filter.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_representation.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_representation.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_initialization.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_initialization.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_smoothers\\_univariate_diffuse.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_smoothers\\_univariate_diffuse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_smoothers\\_univariate.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_smoothers\\_univariate.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_smoothers\\_conventional.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_smoothers\\_conventional.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_smoothers\\_classical.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_smoothers\\_classical.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_smoothers\\_alternative.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_smoothers\\_alternative.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_filters\\_univariate_diffuse.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_filters\\_univariate_diffuse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_filters\\_univariate.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_filters\\_univariate.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_filters\\_inversions.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_filters\\_inversions.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('statsmodels\\tsa\\statespace\\_filters\\_conventional.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\_filters\\_conventional.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPrintSupport.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('scipy\\special\\libsf_error_state.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\scipy\\special\\libsf_error_state.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python313\\python3.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python313\\DLLs\\sqlite3.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('resources\\help\\USER_GUIDE.txt',
   'C:\\appazam\\resterant2\\resources\\help\\USER_GUIDE.txt',
   'DATA'),
  ('resources\\icons\\12.jpg',
   'C:\\appazam\\resterant2\\resources\\icons\\12.jpg',
   'DATA'),
  ('resources\\icons\\background.png',
   'C:\\appazam\\resterant2\\resources\\icons\\background.png',
   'DATA'),
  ('resources\\icons\\smart.ico',
   'C:\\appazam\\resterant2\\resources\\icons\\smart.ico',
   'DATA'),
  ('resources\\styles\\app_style.qss',
   'C:\\appazam\\resterant2\\resources\\styles\\app_style.qss',
   'DATA'),
  ('resources\\styles\\background.jpg',
   'C:\\appazam\\resterant2\\resources\\styles\\background.jpg',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_binning.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_binning.pyx',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\meson.build',
   'DATA'),
  ('sklearn\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\meson.build',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.pyx',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pyx.tp',
   'DATA'),
  ('sklearn\\svm\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\meson.build',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pxd.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pxd.tp',
   'DATA'),
  ('sklearn\\_isotonic.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\_isotonic.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\_cd_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.pyx',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pxd.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pxd.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1-s-dact.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1-s-dact.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_dbscan_inner.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_sorting.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_sorting.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jd-3.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jd-3.json.gz',
   'DATA'),
  ('sklearn\\linear_model\\_sgd_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdq-40945.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdq-40945.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pxd.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pxd.tp',
   'DATA'),
  ('sklearn\\feature_extraction\\_hashing_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\_hashing_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\_libsvm.pxi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm.pxi',
   'DATA'),
  ('sklearn\\datasets\\data\\linnerud_exercise.csv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\linnerud_exercise.csv',
   'DATA'),
  ('sklearn\\utils\\src\\MurmurHash3.h',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\src\\MurmurHash3.h',
   'DATA'),
  ('sklearn\\datasets\\data\\linnerud_physiological.csv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\linnerud_physiological.csv',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\data-v1-dl-21854866.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\data-v1-dl-21854866.arff.gz',
   'DATA'),
  ('sklearn\\cluster\\_k_means_elkan.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.pyx',
   'DATA'),
  ('sklearn\\datasets\\data\\iris.csv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\iris.csv',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdq-62.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdq-62.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pxd.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pxd.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-dv-1.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-s-act-.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.pxd',
   'DATA'),
  ('sklearn\\cluster\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_invalid_order.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_invalid_order.txt',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\data-v1-dl-21552912.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\data-v1-dl-21552912.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-dv-1.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_k_means_minibatch.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\_sag_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_partitioner.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_partitioner.pxd',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.pyx',
   'DATA'),
  ('sklearn\\svm\\src\\newrand\\newrand.h',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\newrand\\newrand.h',
   'DATA'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jd-61.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jd-61.json.gz',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\feature_extraction\\_hashing_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\_hashing_fast.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-s-act-.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\diabetes_target.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\diabetes_target.csv.gz',
   'DATA'),
  ('sklearn\\utils\\_sorting.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_sorting.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\README.md',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\README.md',
   'DATA'),
  ('sklearn\\decomposition\\_cdnmf_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\decomposition\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\meson.build',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdq-42585.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdq-42585.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_gradient_boosting.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_gradient_boosting.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_splitter.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_splitter.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-dv-1.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\wine_data.csv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\wine_data.csv',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.pyx',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.pyx.tp',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdf-561.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdf-561.json.gz',
   'DATA'),
  ('sklearn\\utils\\_isfinite.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_isfinite.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.pyx',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.pxd',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\estimator.css',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\estimator.css',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\meson.build',
   'DATA'),
  ('sklearn\\preprocessing\\_target_encoder_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\linear.cpp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\linear.cpp',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\estimator.js',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\estimator.js',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\_loss\\_loss.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\_loss\\_loss.pxd',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\README.md',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\README.md',
   'DATA'),
  ('sklearn\\utils\\_random.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_random.pxd',
   'DATA'),
  ('sklearn\\utils\\_heap.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_heap.pyx',
   'DATA'),
  ('sklearn\\linear_model\\_cd_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jd-40589.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jd-40589.json.gz',
   'DATA'),
  ('sklearn\\datasets\\images\\flower.jpg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\images\\flower.jpg',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.pyx',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\tron.h',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\tron.h',
   'DATA'),
  ('sklearn\\utils\\arrayfuncs.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.pyx',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\libsvm_sparse_helper.c',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\libsvm_sparse_helper.c',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\libsvm_template.cpp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\libsvm_template.cpp',
   'DATA'),
  ('sklearn\\datasets\\descr\\lfw.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\lfw.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\diabetes.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\diabetes.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdf-40589.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdf-40589.json.gz',
   'DATA'),
  ('sklearn\\tree\\_utils.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_utils.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\manifold\\_utils.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_utils.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-dv-1.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\histogram.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\histogram.pyx',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_classmode.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_classmode.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\data-v1-dl-52352.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\data-v1-dl-52352.arff.gz',
   'DATA'),
  ('sklearn\\tree\\_criterion.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_criterion.pxd',
   'DATA'),
  ('sklearn\\neighbors\\_binary_tree.pxi.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_binary_tree.pxi.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-s-act-.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\data-v1-dl-1666876.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\data-v1-dl-1666876.arff.gz',
   'DATA'),
  ('sklearn\\_loss\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\_loss\\meson.build',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pxd',
   'DATA'),
  ('sklearn\\svm\\_newrand.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_newrand.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\data-v1-dl-49822.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\data-v1-dl-49822.arff.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\_liblinear.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_liblinear.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdq-1119.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdq-1119.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-s-act-.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\meson.build',
   'DATA'),
  ('sklearn\\utils\\sparsefuncs_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\splitting.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\splitting.pyx',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.pyx',
   'DATA'),
  ('sklearn\\preprocessing\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-40981.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-40981.json.gz',
   'DATA'),
  ('sklearn\\utils\\_typedefs.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_typedefs.pyx',
   'DATA'),
  ('sklearn\\manifold\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\manifold\\meson.build',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\data-v1-dl-61.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\data-v1-dl-61.arff.gz',
   'DATA'),
  ('sklearn\\svm\\_newrand.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_newrand.pyx',
   'DATA'),
  ('sklearn\\datasets\\descr\\rcv1.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\rcv1.rst',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.pxd.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.pxd.tp',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\LICENSE',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\LICENSE',
   'DATA'),
  ('sklearn\\datasets\\images\\README.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\images\\README.txt',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_splitter.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_splitter.pyx',
   'DATA'),
  ('sklearn\\utils\\_typedefs.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_typedefs.pxd',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-292.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-292.json.gz',
   'DATA'),
  ('sklearn\\preprocessing\\_target_encoder_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.pyx',
   'DATA'),
  ('sklearn\\utils\\_sorting.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_sorting.pyx',
   'DATA'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.pyx',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-292.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-292.json.gz',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\LIBSVM_CHANGES',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\LIBSVM_CHANGES',
   'DATA'),
  ('sklearn\\datasets\\descr\\twenty_newsgroups.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\twenty_newsgroups.rst',
   'DATA'),
  ('sklearn\\svm\\_liblinear.pxi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_liblinear.pxi',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_heap.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_heap.pxd',
   'DATA'),
  ('sklearn\\metrics\\cluster\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\cluster\\meson.build',
   'DATA'),
  ('sklearn\\_loss\\_loss.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\_loss\\_loss.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\linear.h',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\linear.h',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdf-2.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdf-2.json.gz',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdq-1590.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdq-1590.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdf-1.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdf-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\linnerud.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\linnerud.rst',
   'DATA'),
  ('sklearn\\utils\\_random.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_random.pyx',
   'DATA'),
  ('sklearn\\datasets\\descr\\california_housing.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\california_housing.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdf-40945.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdf-40945.json.gz',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.pyx.tp',
   'DATA'),
  ('sklearn\\__check_build\\_check_build.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.pyx',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\_cython_blas_helpers.h',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\_cython_blas_helpers.h',
   'DATA'),
  ('sklearn\\cluster\\_k_means_minibatch.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.pyx',
   'DATA'),
  ('sklearn\\utils\\murmurhash.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\murmurhash.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdf-40966.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdf-40966.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jd-40945.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jd-40945.json.gz',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.pyx',
   'DATA'),
  ('sklearn\\svm\\_libsvm.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\decomposition\\_online_lda_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\LICENSE',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\LICENSE',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\descr\\digits.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\digits.rst',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\tron.cpp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\tron.cpp',
   'DATA'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\tree\\_criterion.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_criterion.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_criterion.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_criterion.pyx',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.pxd.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.pxd.tp',
   'DATA'),
  ('sklearn\\svm\\_libsvm_sparse.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.pyx',
   'DATA'),
  ('sklearn\\tree\\_splitter.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_splitter.pxd',
   'DATA'),
  ('sklearn\\utils\\_heap.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_heap.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\images\\china.jpg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\images\\china.jpg',
   'DATA'),
  ('sklearn\\tree\\_utils.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_utils.pyx',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-dv-3.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-dv-3.json.gz',
   'DATA'),
  ('sklearn\\decomposition\\_cdnmf_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.pyx',
   'DATA'),
  ('sklearn\\feature_extraction\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\feature_extraction\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jd-40966.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jd-40966.json.gz',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.pxd',
   'DATA'),
  ('sklearn\\utils\\sparsefuncs_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.pyx',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pyx.tp',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\svm.h',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\svm.h',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdq-40589.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdq-40589.json.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\breast_cancer.csv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\breast_cancer.csv',
   'DATA'),
  ('sklearn\\tree\\_partitioner.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_partitioner.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdf-42074.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdf-42074.json.gz',
   'DATA'),
  ('sklearn\\neighbors\\_kd_tree.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\data\\digits.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\digits.csv.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-s-act-.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_multilabel.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_multilabel.txt',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.pyx.tp',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\svm.cpp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\svm.cpp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\data-v1-dl-1595261.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\data-v1-dl-1595261.arff.gz',
   'DATA'),
  ('sklearn\\tree\\_tree.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_tree.pxd',
   'DATA'),
  ('sklearn\\neighbors\\_ball_tree.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.pyx.tp',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\py.typed',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\py.typed',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\data-v1-dl-4965250.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\data-v1-dl-4965250.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdq-3.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdq-3.json.gz',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\_typing.pyi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\_typing.pyi',
   'DATA'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.pyx',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-dv-4.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-dv-4.json.gz',
   'DATA'),
  ('sklearn\\manifold\\_barnes_hut_tsne.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.pyx',
   'DATA'),
  ('sklearn\\_loss\\_loss.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\_loss\\_loss.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-s-act-.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\kddcup99.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\kddcup99.rst',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\params.css',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\params.css',
   'DATA'),
  ('sklearn\\utils\\arrayfuncs.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\data-v1-dl-54002.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\data-v1-dl-54002.arff.gz',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\descr\\iris.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\iris.rst',
   'DATA'),
  ('sklearn\\utils\\murmurhash.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\murmurhash.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdq-40966.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdq-40966.json.gz',
   'DATA'),
  ('sklearn\\neighbors\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\meson.build',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_partitioner.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_partitioner.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jd-40675.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jd-40675.json.gz',
   'DATA'),
  ('sklearn\\svm\\_libsvm.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm.pyx',
   'DATA'),
  ('sklearn\\tree\\_tree.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_tree.pyx',
   'DATA'),
  ('sklearn\\neighbors\\_kd_tree.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jd-1119.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jd-1119.json.gz',
   'DATA'),
  ('sklearn\\utils\\_random.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_random.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_typedefs.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_typedefs.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1-s-dact.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1-s-dact.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.pyx.tp',
   'DATA'),
  ('sklearn\\externals\\README',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\externals\\README',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdf-62.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdf-62.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-s-act-.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_utils.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_utils.pxd',
   'DATA'),
  ('sklearn\\decomposition\\_online_lda_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdq-561.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdq-561.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\COPYRIGHT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\COPYRIGHT',
   'DATA'),
  ('sklearn\\utils\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\meson.build',
   'DATA'),
  ('sklearn\\ensemble\\_gradient_boosting.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_gradient_boosting.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\data-v1-dl-1.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\data-v1-dl-1.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\wine_data.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\wine_data.rst',
   'DATA'),
  ('sklearn\\datasets\\descr\\species_distributions.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\species_distributions.rst',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.pxd.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pxd.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-40981.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-40981.json.gz',
   'DATA'),
  ('sklearn\\svm\\_liblinear.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_liblinear.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdq-2.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdq-2.json.gz',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jd-2.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jd-2.json.gz',
   'DATA'),
  ('sklearn\\_isotonic.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\_isotonic.pyx',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jd-561.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jd-561.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jd-1.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jd-1.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jd-1590.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jd-1590.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.pyx',
   'DATA'),
  ('sklearn\\cluster\\_k_means_elkan.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\__check_build\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\meson.build',
   'DATA'),
  ('sklearn\\linear_model\\_sgd_fast.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.pyx.tp',
   'DATA'),
  ('sklearn\\manifold\\_barnes_hut_tsne.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\liblinear_helper.c',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\liblinear_helper.c',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\data-v1-dl-16826755.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\data-v1-dl-16826755.arff.gz',
   'DATA'),
  ('sklearn\\tree\\_tree.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\_sag_fast.pyx.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdf-1590.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdf-1590.json.gz',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.pxd',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdf-3.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdf-3.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_k_means_lloyd.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdq-40675.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdq-40675.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdq-1.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdq-1.json.gz',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\_svm_cython_blas_helpers.h',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\_svm_cython_blas_helpers.h',
   'DATA'),
  ('sklearn\\datasets\\descr\\olivetti_faces.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\olivetti_faces.rst',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jd-42074.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jd-42074.json.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\covtype.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\covtype.rst',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\meson.build',
   'DATA'),
  ('sklearn\\__check_build\\_check_build.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.pxd',
   'DATA'),
  ('sklearn\\datasets\\_svmlight_format_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_svmlight_format_fast.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdq-61.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdq-61.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pxd.tp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pxd.tp',
   'DATA'),
  ('sklearn\\neighbors\\_ball_tree.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\data-v1-dl-4644182.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\data-v1-dl-4644182.arff.gz',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.pyx',
   'DATA'),
  ('sklearn\\datasets\\descr\\breast_cancer.rst',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\descr\\breast_cancer.rst',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\tree\\meson.build',
   'DATA'),
  ('sklearn\\utils\\murmurhash.pxd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\murmurhash.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jd-62.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jd-62.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdq-42074.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdq-42074.json.gz',
   'DATA'),
  ('sklearn\\metrics\\meson.build',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_classification.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_classification.txt',
   'DATA'),
  ('sklearn\\svm\\_libsvm_sparse.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdf-61.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdf-61.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_isfinite.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\_isfinite.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\data-v1-dl-3.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\data-v1-dl-3.arff.gz',
   'DATA'),
  ('sklearn\\cluster\\_dbscan_inner.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdf-42585.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdf-42585.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\data-v1-dl-52739.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\data-v1-dl-52739.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jd-42585.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jd-42585.json.gz',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\libsvm_helper.c',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\libsvm_helper.c',
   'DATA'),
  ('sklearn\\cluster\\_k_means_lloyd.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.pyx',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_fast.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\data\\diabetes_data_raw.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\data\\diabetes_data_raw.csv.gz',
   'DATA'),
  ('sklearn\\datasets\\_svmlight_format_fast.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\_svmlight_format_fast.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-s-act-.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\manifold\\_utils.cp313-win_amd64.lib',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\manifold\\_utils.cp313-win_amd64.lib',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\_compat.pyi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\_compat.pyi',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\data-v1-dl-17928620.arff.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\data-v1-dl-17928620.arff.gz',
   'DATA'),
  ('sklearn\\utils\\src\\MurmurHash3.cpp',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\utils\\src\\MurmurHash3.cpp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdf-1119.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdf-1119.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdf-40675.json.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdf-40675.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_invalid.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_invalid.txt',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.pyx',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.pyx',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\REQUESTED',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('plotly\\package_data\\widgetbundle.js',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\widgetbundle.js',
   'DATA'),
  ('plotly\\package_data\\templates\\ggplot2.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\ggplot2.json',
   'DATA'),
  ('plotly\\package_data\\datasets\\medals.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\medals.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\tips.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\tips.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\iris.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\iris.csv.gz',
   'DATA'),
  ('plotly\\package_data\\templates\\seaborn.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\seaborn.json',
   'DATA'),
  ('plotly\\package_data\\datasets\\carshare.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\carshare.csv.gz',
   'DATA'),
  ('plotly\\package_data\\templates\\simple_white.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\simple_white.json',
   'DATA'),
  ('plotly\\package_data\\plotly.min.js',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\plotly.min.js',
   'DATA'),
  ('plotly\\package_data\\datasets\\experiment.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\experiment.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\election.geojson.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\election.geojson.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\election.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\election.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\wind.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\wind.csv.gz',
   'DATA'),
  ('plotly\\package_data\\templates\\ygridoff.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\ygridoff.json',
   'DATA'),
  ('plotly\\package_data\\datasets\\stocks.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\stocks.csv.gz',
   'DATA'),
  ('plotly\\package_data\\templates\\plotly.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\plotly.json',
   'DATA'),
  ('plotly\\package_data\\templates\\xgridoff.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\xgridoff.json',
   'DATA'),
  ('plotly\\package_data\\templates\\presentation.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\presentation.json',
   'DATA'),
  ('plotly\\package_data\\templates\\plotly_white.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\plotly_white.json',
   'DATA'),
  ('plotly\\package_data\\templates\\gridon.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\gridon.json',
   'DATA'),
  ('plotly\\package_data\\datasets\\gapminder.csv.gz',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\datasets\\gapminder.csv.gz',
   'DATA'),
  ('plotly\\package_data\\templates\\plotly_dark.json',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly\\package_data\\templates\\plotly_dark.json',
   'DATA'),
  ('qt_material\\themes\\dark_lightgreen.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\dark_lightgreen.xml',
   'DATA'),
  ('qt_material\\resources\\source\\uparrow.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\uparrow.svg',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-Regular.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-Regular.ttf',
   'DATA'),
  ('qt_material\\resources\\source\\radiobutton_unchecked_invert.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\radiobutton_unchecked_invert.svg',
   'DATA'),
  ('qt_material\\resources\\source\\checkbox_indeterminate.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checkbox_indeterminate.svg',
   'DATA'),
  ('qt_material\\resources\\source\\rightarrow.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\rightarrow.svg',
   'DATA'),
  ('qt_material\\resources\\logo\\logo.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\logo\\logo.svg',
   'DATA'),
  ('qt_material\\themes\\light_pink.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_pink.xml',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-Thin.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-Thin.ttf',
   'DATA'),
  ('qt_material\\resources\\source\\splitter-vertical.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\splitter-vertical.svg',
   'DATA'),
  ('qt_material\\themes\\light_purple_500.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_purple_500.xml',
   'DATA'),
  ('qt_material\\resources\\source\\checkbox_indeterminate_invert.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checkbox_indeterminate_invert.svg',
   'DATA'),
  ('qt_material\\resources\\source\\branch-more.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\branch-more.svg',
   'DATA'),
  ('qt_material\\themes\\light_red.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_red.xml',
   'DATA'),
  ('qt_material\\resources\\logo\\logo_frame.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\logo\\logo_frame.svg',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-LightItalic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-LightItalic.ttf',
   'DATA'),
  ('qt_material\\fonts\\roboto\\RobotoCondensed-LightItalic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\RobotoCondensed-LightItalic.ttf',
   'DATA'),
  ('qt_material\\themes\\dark_purple.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\dark_purple.xml',
   'DATA'),
  ('qt_material\\resources\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-Italic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-Italic.ttf',
   'DATA'),
  ('qt_material\\themes\\light_lightgreen_500.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_lightgreen_500.xml',
   'DATA'),
  ('qt_material\\themes\\light_teal_500.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_teal_500.xml',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-Black.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-Black.ttf',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-BlackItalic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-BlackItalic.ttf',
   'DATA'),
  ('qt_material\\resources\\source\\float.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\float.svg',
   'DATA'),
  ('qt_material\\themes\\light_cyan_500.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_cyan_500.xml',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-MediumItalic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-MediumItalic.ttf',
   'DATA'),
  ('qt_material\\resources\\source\\base.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\base.svg',
   'DATA'),
  ('qt_material\\themes\\light_amber.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_amber.xml',
   'DATA'),
  ('qt_material\\resources\\source\\leftarrow.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\leftarrow.svg',
   'DATA'),
  ('qt_material\\resources\\source\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('qt_material\\fonts\\roboto\\RobotoCondensed-Bold.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\RobotoCondensed-Bold.ttf',
   'DATA'),
  ('qt_material\\resources\\__init__.py',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\__init__.py',
   'DATA'),
  ('qt_material\\resources\\source\\tab_close.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\tab_close.svg',
   'DATA'),
  ('qt_material\\themes\\light_lightgreen.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_lightgreen.xml',
   'DATA'),
  ('qt_material\\themes\\light_pink_500.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_pink_500.xml',
   'DATA'),
  ('qt_material\\resources\\source\\close.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\close.svg',
   'DATA'),
  ('qt_material\\resources\\source\\downarrow.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\downarrow.svg',
   'DATA'),
  ('qt_material\\themes\\dark_blue.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\dark_blue.xml',
   'DATA'),
  ('qt_material\\themes\\dark_cyan.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\dark_cyan.xml',
   'DATA'),
  ('qt_material\\resources\\source\\checkbox_unchecked.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checkbox_unchecked.svg',
   'DATA'),
  ('qt_material\\resources\\source\\toolbar-handle-horizontal.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\toolbar-handle-horizontal.svg',
   'DATA'),
  ('qt_material\\resources\\source\\checkbox_checked_invert.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checkbox_checked_invert.svg',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-ThinItalic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-ThinItalic.ttf',
   'DATA'),
  ('qt_material\\themes\\dark_red.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\dark_red.xml',
   'DATA'),
  ('qt_material\\themes\\light_red_500.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_red_500.xml',
   'DATA'),
  ('qt_material\\resources\\source\\splitter-horizontal.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\splitter-horizontal.svg',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-Medium.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-Medium.ttf',
   'DATA'),
  ('qt_material\\material.qss.template',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\material.qss.template',
   'DATA'),
  ('qt_material\\resources\\source\\branch-closed.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\branch-closed.svg',
   'DATA'),
  ('qt_material\\themes\\dark_amber.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\dark_amber.xml',
   'DATA'),
  ('qt_material\\resources\\source\\leftarrow2.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\leftarrow2.svg',
   'DATA'),
  ('qt_material\\themes\\light_purple.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_purple.xml',
   'DATA'),
  ('qt_material\\resources\\source\\toolbar-handle-vertical.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\toolbar-handle-vertical.svg',
   'DATA'),
  ('qt_material\\resources\\source\\__init__.py',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\__init__.py',
   'DATA'),
  ('qt_material\\resources\\__pycache__\\generate.cpython-313.pyc',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\__pycache__\\generate.cpython-313.pyc',
   'DATA'),
  ('qt_material\\resources\\logo\\__init__.py',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\logo\\__init__.py',
   'DATA'),
  ('qt_material\\resources\\source\\sizegrip.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\sizegrip.svg',
   'DATA'),
  ('qt_material\\fonts\\roboto\\RobotoCondensed-Italic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\RobotoCondensed-Italic.ttf',
   'DATA'),
  ('qt_material\\resources\\source\\checklist_indeterminate_invert.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checklist_indeterminate_invert.svg',
   'DATA'),
  ('qt_material\\resources\\source\\checklist_indeterminate.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checklist_indeterminate.svg',
   'DATA'),
  ('qt_material\\resources\\generate.py',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\generate.py',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-Bold.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-Bold.ttf',
   'DATA'),
  ('qt_material\\themes\\light_yellow.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_yellow.xml',
   'DATA'),
  ('qt_material\\resources\\source\\checklist_invert.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checklist_invert.svg',
   'DATA'),
  ('qt_material\\resources\\source\\radiobutton_checked.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\radiobutton_checked.svg',
   'DATA'),
  ('qt_material\\themes\\light_teal.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_teal.xml',
   'DATA'),
  ('qt_material\\fonts\\roboto\\RobotoCondensed-BoldItalic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\RobotoCondensed-BoldItalic.ttf',
   'DATA'),
  ('qt_material\\resources\\source\\branch-open.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\branch-open.svg',
   'DATA'),
  ('qt_material\\themes\\light_blue.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_blue.xml',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-BoldItalic.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-BoldItalic.ttf',
   'DATA'),
  ('qt_material\\resources\\source\\uparrow2.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\uparrow2.svg',
   'DATA'),
  ('qt_material\\dock_theme.ui',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\dock_theme.ui',
   'DATA'),
  ('qt_material\\themes\\dark_teal.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\dark_teal.xml',
   'DATA'),
  ('qt_material\\resources\\source\\vline.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\vline.svg',
   'DATA'),
  ('qt_material\\fonts\\roboto\\Roboto-Light.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\Roboto-Light.ttf',
   'DATA'),
  ('qt_material\\resources\\source\\radiobutton_unchecked.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\radiobutton_unchecked.svg',
   'DATA'),
  ('qt_material\\themes\\light_orange.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_orange.xml',
   'DATA'),
  ('qt_material\\themes\\dark_pink.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\dark_pink.xml',
   'DATA'),
  ('qt_material\\themes\\light_cyan.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_cyan.xml',
   'DATA'),
  ('qt_material\\fonts\\roboto\\RobotoCondensed-Light.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\RobotoCondensed-Light.ttf',
   'DATA'),
  ('qt_material\\resources\\logo\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\logo\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('qt_material\\themes\\dark_yellow.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\dark_yellow.xml',
   'DATA'),
  ('qt_material\\fonts\\roboto\\RobotoCondensed-Regular.ttf',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\fonts\\roboto\\RobotoCondensed-Regular.ttf',
   'DATA'),
  ('qt_material\\resources\\source\\branch-end.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\branch-end.svg',
   'DATA'),
  ('qt_material\\resources\\source\\checkbox_checked.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checkbox_checked.svg',
   'DATA'),
  ('qt_material\\resources\\source\\checklist.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checklist.svg',
   'DATA'),
  ('qt_material\\resources\\source\\checkbox_unchecked_invert.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\checkbox_unchecked_invert.svg',
   'DATA'),
  ('qt_material\\resources\\source\\radiobutton_checked_invert.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\radiobutton_checked_invert.svg',
   'DATA'),
  ('qt_material\\resources\\source\\downarrow2.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\downarrow2.svg',
   'DATA'),
  ('qt_material\\resources\\source\\slider.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\slider.svg',
   'DATA'),
  ('qt_material\\resources\\source\\rightarrow2.svg',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\resources\\source\\rightarrow2.svg',
   'DATA'),
  ('qt_material\\themes\\light_blue_500.xml',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\qt_material\\themes\\light_blue_500.xml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('plotly-6.1.2.dist-info\\entry_points.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly-6.1.2.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\REQUESTED',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('plotly-6.1.2.dist-info\\top_level.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly-6.1.2.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\RECORD',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('plotly-6.1.2.dist-info\\WHEEL',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly-6.1.2.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.0.dist-info\\entry_points.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\LICENSE.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('plotly-6.1.2.dist-info\\METADATA',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly-6.1.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('plotly-6.1.2.dist-info\\REQUESTED',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly-6.1.2.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.0.dist-info\\DELVEWHEEL',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.0.dist-info\\INSTALLER',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('plotly-6.1.2.dist-info\\RECORD',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly-6.1.2.dist-info\\RECORD',
   'DATA'),
  ('plotly-6.1.2.dist-info\\licenses\\LICENSE.txt',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly-6.1.2.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info\\METADATA',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.0.dist-info\\WHEEL',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\numpy-2.3.0.dist-info\\WHEEL',
   'DATA'),
  ('plotly-6.1.2.dist-info\\INSTALLER',
   'C:\\appazam\\resterant2\\.venv\\Lib\\site-packages\\plotly-6.1.2.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'C:\\appazam\\resterant2\\build\\main\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
