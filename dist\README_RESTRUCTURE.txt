# خطة إعادة هيكلة وتحسين النظام

## 1. تنظيم الملفات
- views/      → جميع نوافذ الواجهة (PyQt5)
- models/     → منطق قاعدة البيانات
- utils/      → أدوات مساعدة مشتركة (جديد)
- resources/  → أيقونات، صور، ملفات QSS

## 2. الأدوات المساعدة (utils/utilities.py)
- load_settings: تحميل إعدادات النظام حسب النشاط
- parse_permissions: توحيد الصلاحيات لقائمة
- has_permission: التحقق من الصلاحية

## 3. إزالة التكرار
- استخدم دوال utils/utilities.py في جميع النوافذ بدل تكرار منطق الصلاحيات أو تحميل الإعدادات.

## 4. دعم الأنظمة المتعددة
- جميع المسارات عبر os.path
- عزل منطق الواجهة عن المنطق العام قدر الإمكان

## 5. الخطوات القادمة
- نقل الأكواد المتكررة في views إلى utils
- توحيد رسائل الخطأ والتنبيهات
- دراسة نقل الواجهة إلى Kivy أو BeeWare لدعم أندرويد مستقبلاً

---
أي نافذة جديدة أو منطق جديد يجب أن يستخدم الأدوات المساعدة من utils.
