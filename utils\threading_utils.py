from PyQt5.QtCore import QThread, pyqtSignal

class WorkerThread(QThread):
    result_ready = pyqtSignal(object)
    error = pyqtSignal(Exception)

    def __init__(self, fn, *args, **kwargs):
        super().__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self._result = None
        self._error = None

    def run(self):
        try:
            self._result = self.fn(*self.args, **self.kwargs)
            self.result_ready.emit(self._result)
        except Exception as e:
            self._error = e
            self.error.emit(e)
