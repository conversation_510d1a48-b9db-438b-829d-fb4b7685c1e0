from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from PyQt5.QtGui import QTextDocument


def get_icon(icon_name):
    """
    إرجاع المسار الصحيح للأيقونة (png أو ico) سواء في التطوير أو بعد التحزيم.
    icon_name: اسم الملف فقط مثل 'smart.ico' أو 'add.png'
    """
    import sys, os
    if hasattr(sys, 'frozen'):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    return os.path.join(base_path, 'resources', 'icons', icon_name)

def print_invoice(html_content, parent=None):
    """
    طباعة فاتورة أو سند باستخدام HTML/QTextDocument.
    html_content: نص HTML يمثل الفاتورة أو السند.
    parent: النافذة الأم (اختياري).
    """
    printer = QPrinter(QPrinter.HighResolution)
    dialog = QPrintDialog(printer, parent)
    if dialog.exec_() == QPrintDialog.Accepted:
        doc = QTextDocument()
        doc.setHtml(html_content)
        doc.print_(printer)
