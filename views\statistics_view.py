# نافذة الإحصائيات
# تعرض ملخص المبيعات والأرباح والخسارة بشكل بياني
# تاريخ آخر تعديل: 2025-05-10

from PyQt5 import QtWidgets, QtGui, QtCore
import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from models import database
import seaborn as sns
import arabic_reshaper
from bidi.algorithm import get_display
import matplotlib
from utils.utilities import parse_permissions, load_settings, get_cogs_fifo
from utils.messages import get_message
from utils.threading_utils import WorkerThread
from main import get_app_icon
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['font.family'] = ['Cairo', 'Amiri', 'Arial', 'Tahoma', 'sans-serif']

# دالة مساعدة لإعادة تشكيل النص العربي مع اتجاه RTL
def ar_text(text):
    reshaped = arabic_reshaper.reshape(text)
    return get_display(reshaped)

# نافذة واحدة فقط لكل نوع
open_windows = {}

def show_singleton_window(window_class, *args, **kwargs):
    global open_windows
    if window_class in open_windows:
        win = open_windows[window_class]
        win.raise_()
        win.activateWindow()
        win.showNormal()
        return win
    win = window_class(*args, **kwargs)
    open_windows[window_class] = win
    win.show()
    def on_close(event=None):
        if window_class in open_windows:
            del open_windows[window_class]
    win.destroyed.connect(on_close)
    return win

class StatisticsWindow(QtWidgets.QWidget):
    def __init__(self, username, role, permissions=None):
        super().__init__()
        self.setWindowIcon(get_app_icon())
        self.username = username  # اسم المستخدم الحالي
        self.role = role          # دور المستخدم
        from utils.utilities import parse_permissions
        self.permissions = parse_permissions(permissions)
        self.settings = load_settings()  # تحميل الإعدادات العامة للنشاط
        # منع الدخول إذا لم تتوفر صلاحية view_reports (إلا للمدير)
        if self.role != 'manager' and 'view_reports' not in self.permissions:
            QtWidgets.QMessageBox.critical(self, 'صلاحيات غير كافية', get_message('error_permission'))
            self.close()
            return
        self.setWindowTitle('إحصائيات وتحليلات متقدمة')
        self.setGeometry(400, 150, 1000, 700)
        from utils.print_utils import get_icon
        icon_path = get_icon('smart.ico')
        if QtCore.QFile.exists(icon_path):
            self.setWindowIcon(QtGui.QIcon(icon_path))
        self.setup_ui()
        # عند الفتح: حمل فقط الإحصائيات العامة (أسرع)
        self.load_statistics()
        # باقي التحليلات تُحمّل عند اختيار التبويب فقط (Lazy Loading)

    def setup_ui(self):
        """
        إعداد واجهة المستخدم لنافذة الإحصائيات.
        """
        layout = QtWidgets.QVBoxLayout()
        self.tabs = QtWidgets.QTabWidget()
        # تبويب الإحصائيات العامة
        stats_tab = QtWidgets.QWidget()
        stats_layout = QtWidgets.QVBoxLayout(stats_tab)
        self.period_combo = QtWidgets.QComboBox()  # قائمة اختيار الفترة
        self.period_combo.addItems(['يومي', 'شهري', 'سنوي'])
        self.period_combo.currentIndexChanged.connect(self.load_statistics)
        stats_layout.addWidget(self.period_combo)

        self.table = QtWidgets.QTableWidget()  # جدول عرض البيانات
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['الفترة', 'إجمالي المبيعات', 'إجمالي الأرباح', 'إجمالي الخسارة', 'عدد العمليات'])
        self.table.horizontalHeader().setStretchLastSection(True)
        stats_layout.addWidget(self.table)

        self.figure = plt.Figure(figsize=(5,2))  # إعداد الرسم البياني
        self.canvas = FigureCanvas(self.figure)
        stats_layout.addWidget(self.canvas)

        # جدول ملخص التحاليل الكمية والنوعية
        self.summary_table = QtWidgets.QTableWidget()
        self.summary_table.setColumnCount(5)
        self.summary_table.setHorizontalHeaderLabels(['المؤشر', 'المبيعات', 'الأرباح', 'الخسارة', 'رأس المال'])
        self.summary_table.horizontalHeader().setStretchLastSection(True)
        stats_layout.addWidget(QtWidgets.QLabel('ملخص التحاليل الكمية والنوعية:'))
        stats_layout.addWidget(self.summary_table)

        self.tabs.addTab(stats_tab, 'الإحصائيات العامة')

        # تبويب المنتجات الأكثر والأقل مبيعًا
        products_tab = QtWidgets.QWidget()
        products_layout = QtWidgets.QVBoxLayout(products_tab)
        self.top_products_table = QtWidgets.QTableWidget()
        self.top_products_table.setColumnCount(5)
        self.top_products_table.setHorizontalHeaderLabels([
            'المنتج', 'الكمية المباعة', 'إجمالي المبيعات', 'إجمالي الربح', 'نسبة الربح %'])
        self.top_products_table.horizontalHeader().setStretchLastSection(True)
        products_layout.addWidget(QtWidgets.QLabel('المنتجات الأكثر مبيعًا:'))
        products_layout.addWidget(self.top_products_table)
        self.least_products_table = QtWidgets.QTableWidget()
        self.least_products_table.setColumnCount(5)
        self.least_products_table.setHorizontalHeaderLabels([
            'المنتج', 'الكمية المباعة', 'إجمالي المبيعات', 'إجمالي الربح', 'نسبة الربح %'])
        self.least_products_table.horizontalHeader().setStretchLastSection(True)
        products_layout.addWidget(QtWidgets.QLabel('المنتجات الأقل مبيعًا:'))
        products_layout.addWidget(self.least_products_table)
        self.tabs.addTab(products_tab, 'تحليل المنتجات')

        # تبويب التنبيهات الذكية
        alerts_tab = QtWidgets.QWidget()
        alerts_layout = QtWidgets.QVBoxLayout(alerts_tab)
        self.alerts_list = QtWidgets.QListWidget()
        alerts_layout.addWidget(QtWidgets.QLabel('تنبيهات ذكية:'))
        alerts_layout.addWidget(self.alerts_list)
        self.tabs.addTab(alerts_tab, 'التنبيهات')

        # تبويب توقع المبيعات
        forecast_tab = QtWidgets.QWidget()
        forecast_layout = QtWidgets.QVBoxLayout(forecast_tab)
        self.forecast_canvas = FigureCanvas(plt.Figure(figsize=(5,2)))
        forecast_layout.addWidget(self.forecast_canvas)
        self.forecast_status = QtWidgets.QLabel('')
        forecast_layout.addWidget(self.forecast_status)
        self.tabs.addTab(forecast_tab, 'توقع المبيعات')

        # تبويب تحليل الارتباط
        corr_tab = QtWidgets.QWidget()
        corr_layout = QtWidgets.QVBoxLayout(corr_tab)
        self.corr_canvas = FigureCanvas(plt.Figure(figsize=(4,3)))
        corr_layout.addWidget(self.corr_canvas)
        self.corr_table = QtWidgets.QTableWidget()
        corr_layout.addWidget(self.corr_table)
        self.tabs.addTab(corr_tab, 'تحليل الارتباط')

        # تبويب تحليل الانحدار
        reg_tab = QtWidgets.QWidget()
        reg_layout = QtWidgets.QVBoxLayout(reg_tab)
        self.reg_canvas = FigureCanvas(plt.Figure(figsize=(4,3)))
        reg_layout.addWidget(self.reg_canvas)
        self.reg_results = QtWidgets.QTableWidget()
        reg_layout.addWidget(self.reg_results)
        self.reg_status = QtWidgets.QLabel('')
        reg_layout.addWidget(self.reg_status)
        self.tabs.addTab(reg_tab, 'تحليل الانحدار')

        # تبويب التحليل العنقودي
        cluster_tab = QtWidgets.QWidget()
        cluster_layout = QtWidgets.QVBoxLayout(cluster_tab)
        self.cluster_canvas = FigureCanvas(plt.Figure(figsize=(5,3)))
        cluster_layout.addWidget(self.cluster_canvas)
        self.cluster_status = QtWidgets.QLabel('')
        cluster_layout.addWidget(self.cluster_status)
        self.tabs.addTab(cluster_tab, 'التحليل العنقودي')

        layout.addWidget(self.tabs)
        self.setLayout(layout)
        # ربط تغيير التبويب لتحميل البيانات عند الطلب فقط
        self.tabs.currentChanged.connect(self.on_tab_changed)
        # دعم RTL والخط العربي
        self.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.tabs.setLayoutDirection(QtCore.Qt.RightToLeft)
        # توحيد الخطوط
        self.setStyleSheet('font-family: "Cairo", "Tajawal", "Amiri", "Arial", sans-serif; font-size: 13pt;')

        # زر تحديث الإحصائيات
        from utils.print_utils import get_icon
        btns_layout = QtWidgets.QHBoxLayout()
        refresh_btn = QtWidgets.QPushButton('تحديث البيانات')
        refresh_btn.setIcon(QtGui.QIcon(get_icon('refresh.png')))
        refresh_btn.setStyleSheet('background-color: #43a047; color: white; font-weight: bold; margin-left: 12px;')
        refresh_btn.clicked.connect(self.refresh_all)
        btns_layout.addWidget(refresh_btn)
        # زر طباعة الإحصائيات
        print_btn = QtWidgets.QPushButton('طباعة الإحصائيات')
        print_btn.setIcon(QtGui.QIcon(get_icon('smart.ico')))
        print_btn.setStyleSheet('background-color: #1976d2; color: white; font-weight: bold;')
        print_btn.clicked.connect(self.print_current_statistics)
        btns_layout.addWidget(print_btn)
        btns_layout.addStretch()
        layout.addLayout(btns_layout)

    def on_tab_changed(self, idx):
        """
        تحميل بيانات التبويب المطلوب فقط عند اختياره لأول مرة (Lazy Loading)
        """
        tab_name = self.tabs.tabText(idx)
        if not hasattr(self, '_loaded_tabs'):
            self._loaded_tabs = set()
        if tab_name in self._loaded_tabs:
            return
        if tab_name == 'تحليل المنتجات':
            self.load_top_products()
        elif tab_name == 'التنبيهات':
            self.load_alerts()
        elif tab_name == 'توقع المبيعات':
            self.load_forecast()
        elif tab_name == 'تحليل الارتباط':
            self.load_correlation()
        elif tab_name == 'تحليل الانحدار':
            self.load_regression()
        elif tab_name == 'التحليل العنقودي':
            self.load_clustering()
        self._loaded_tabs.add(tab_name)

    def load_statistics(self):
        """
        تحميل بيانات الإحصائيات حسب الفترة المختارة (يومي/شهري/سنوي) وعرضها في الجدول والرسم البياني.
        الآن تعمل في Thread منفصل لتسريع الأداء ومنع التجميد.
        """
        self.table.setRowCount(0)
        self.summary_table.setRowCount(0)
        self.figure.clear()
        self.canvas.draw()
        self._statistics_thread = WorkerThread(self._load_statistics_data, self.period_combo.currentText())
        self._statistics_thread.result_ready.connect(self._on_statistics_ready)
        self._statistics_thread.error.connect(self._on_statistics_error)
        self._statistics_thread.start()

    def _load_statistics_data(self, period):
        import arabic_reshaper
        from bidi.algorithm import get_display
        import sqlite3
        import pandas as pd
        from utils.utilities import get_cogs_fifo
        conn = sqlite3.connect(database.DB_NAME)
        if period == 'يومي':
            sales_query = '''SELECT sale_date as period, si.product_id, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales, COUNT(*) as count FROM sales_items si JOIN sales s ON si.sale_id=s.id GROUP BY sale_date, si.product_id ORDER BY sale_date DESC LIMIT 300'''
        elif period == 'شهري':
            sales_query = '''SELECT substr(s.sale_date,1,7) as period, si.product_id, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales, COUNT(*) as count FROM sales_items si JOIN sales s ON si.sale_id=s.id GROUP BY substr(s.sale_date,1,7), si.product_id ORDER BY period DESC LIMIT 120'''
        else:
            sales_query = '''SELECT substr(s.sale_date,1,4) as period, si.product_id, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales, COUNT(*) as count FROM sales_items si JOIN sales s ON si.sale_id=s.id GROUP BY substr(s.sale_date,1,4), si.product_id ORDER BY period DESC LIMIT 50'''
        df_sales = pd.read_sql_query(sales_query, conn)
        cogs_dict = {}
        for period_val in df_sales['period'].unique():
            period_sales = df_sales[df_sales['period'] == period_val]
            cogs = 0.0
            for _, row in period_sales.iterrows():
                cogs += get_cogs_fifo(row['product_id'], row['qty'])
            cogs_dict[period_val] = cogs
        df_grouped = df_sales.groupby('period').agg({'sales': 'sum', 'count': 'sum'}).reset_index()
        df_grouped['cost'] = df_grouped['period'].map(cogs_dict)
        df_grouped['profit'] = df_grouped['sales'] - df_grouped['cost']
        df_grouped['loss'] = df_grouped['cost'] - df_grouped['sales']
        df_grouped['loss'] = df_grouped['loss'].apply(lambda x: x if x > 0 else 0)
        df_grouped['period'] = df_grouped['period'].apply(lambda x: get_display(arabic_reshaper.reshape(str(x))))
        # تجهيز بيانات الجدول والرسم والملخص
        summary = [
            ['المتوسط الحسابي', df_grouped['sales'].mean(), df_grouped['profit'].mean(), df_grouped['loss'].mean(), df_grouped['cost'].sum()/len(df_grouped) if len(df_grouped) else 0],
            ['الانحراف المعياري', df_grouped['sales'].std(), df_grouped['profit'].std(), df_grouped['loss'].std(), 0],
            ['أعلى قيمة', df_grouped['sales'].max(), df_grouped['profit'].max(), df_grouped['loss'].max(), df_grouped['cost'].sum()],
            ['أقل قيمة', df_grouped['sales'].min(), df_grouped['profit'].min(), df_grouped['loss'].min(), 0],
            ['المجموع الكلي', df_grouped['sales'].sum(), df_grouped['profit'].sum(), df_grouped['loss'].sum(), df_grouped['cost'].sum()],
        ]
        # بيانات الرسم
        chart_data = {
            'labels': df_grouped['period'].tolist(),
            'sales': df_grouped['sales'].tolist(),
            'profits': df_grouped['profit'].tolist(),
            'losses': df_grouped['loss'].tolist(),
        }
        return {'df_grouped': df_grouped, 'summary': summary, 'chart_data': chart_data}

    def _on_statistics_ready(self, result):
        df_grouped = result['df_grouped']
        summary = result['summary']
        chart_data = result['chart_data']
        self.table.setRowCount(len(df_grouped))
        for i, row in df_grouped.iterrows():
            self.table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(row['period'])))
            self.table.setItem(i, 1, QtWidgets.QTableWidgetItem(f"{row['sales']:.2f}"))
            self.table.setItem(i, 2, QtWidgets.QTableWidgetItem(f"{row['profit']:.2f}"))
            self.table.setItem(i, 3, QtWidgets.QTableWidgetItem(f"{row['loss']:.2f}"))
            self.table.setItem(i, 4, QtWidgets.QTableWidgetItem(str(int(row['count']))))
        # رسم بياني
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        labels = chart_data['labels']
        sales = chart_data['sales']
        profits = chart_data['profits']
        losses = chart_data['losses']
        bars = ax.bar(labels, sales, color='#4F8A8B', label=ar_text('المبيعات'))
        ax.bar(labels, profits, color='#43a047', label=ar_text('الأرباح'), alpha=0.7)
        ax.bar(labels, losses, color='#e53935', label=ar_text('الخسارة'), alpha=0.5)
        for bar in bars:
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height(), f'{bar.get_height()}', ha='center', va='bottom', fontsize=12, fontweight='bold')
        ax.set_title(ar_text('ملخص المبيعات والأرباح والخسارة'), fontsize=16, fontweight='bold')
        ax.set_ylabel(ar_text('القيمة'), fontsize=14)
        ax.set_xlabel(ar_text('الفترة'), fontsize=14)
        ax.legend(loc='upper right', fontsize=12)
        ax.invert_xaxis()
        self.figure.tight_layout()
        self.canvas.draw()
        # ملخص
        self.summary_table.setRowCount(len(summary))
        for i, row in enumerate(summary):
            for j, val in enumerate(row):
                if j == 0:
                    self.summary_table.setItem(i, j, QtWidgets.QTableWidgetItem(str(val)))
                else:
                    self.summary_table.setItem(i, j, QtWidgets.QTableWidgetItem(f"{val:.2f}"))

    def _on_statistics_error(self, error):
        QtWidgets.QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل الإحصائيات:\n{error}')

    def load_top_products(self):
        """
        تحميل بيانات المنتجات الأكثر والأقل مبيعًا.
        """
        self.top_products_table.setRowCount(0)
        self.least_products_table.setRowCount(0)
        self._top_products_thread = WorkerThread(self._load_top_products_data)
        self._top_products_thread.result_ready.connect(self._on_top_products_ready)
        self._top_products_thread.error.connect(self._on_top_products_error)
        self._top_products_thread.start()

    def _load_top_products_data(self):
        import sqlite3
        import pandas as pd
        from utils.utilities import get_cogs_fifo
        conn = sqlite3.connect(database.DB_NAME)
        # جلب بيانات المنتجات الأكثر مبيعًا
        query = '''SELECT p.id, p.name, SUM(si.quantity) as total_qty, SUM(si.quantity*si.sale_price) as total_sales, \
                          SUM(si.discount) as total_discount
                   FROM sales_items si JOIN products p ON si.product_id=p.id\
                   GROUP BY p.id, p.name ORDER BY total_qty DESC LIMIT 10'''
        df = pd.read_sql_query(query, conn)
        # جلب بيانات المنتجات الأقل مبيعًا
        query2 = '''SELECT p.id, p.name, SUM(si.quantity) as total_qty, SUM(si.quantity*si.sale_price) as total_sales, \
                           SUM(si.discount) as total_discount
                    FROM sales_items si JOIN products p ON si.product_id=p.id\
                    GROUP BY p.id, p.name ORDER BY total_qty ASC LIMIT 10'''
        df2 = pd.read_sql_query(query2, conn)
        conn.close()
        # حساب التكلفة والربح لكل منتج
        def calc_profit(row):
            cogs = get_cogs_fifo(row['id'], row['total_qty']) if row['total_qty'] else 0
            profit = row['total_sales'] - cogs - (row['total_discount'] if row['total_discount'] else 0)
            profit_percent = (profit / row['total_sales'] * 100) if row['total_sales'] else 0
            return pd.Series({'total_profit': profit, 'profit_percentage': profit_percent, 'cogs': cogs})
        if not df.empty:
            df = df.join(df.apply(calc_profit, axis=1))
        if not df2.empty:
            df2 = df2.join(df2.apply(calc_profit, axis=1))
        return {'df': df, 'df2': df2}

    def _on_top_products_ready(self, result):
        df = result['df']
        df2 = result['df2']
        self.top_products_table.setRowCount(len(df))
        for i, row in df.iterrows():
            self.top_products_table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(row['name'])))
            self.top_products_table.setItem(i, 1, QtWidgets.QTableWidgetItem(str(int(row['total_qty']))))
            self.top_products_table.setItem(i, 2, QtWidgets.QTableWidgetItem(f"{row['total_sales']:.2f}"))
            self.top_products_table.setItem(i, 3, QtWidgets.QTableWidgetItem(f"{row['total_profit']:.2f}"))
            self.top_products_table.setItem(i, 4, QtWidgets.QTableWidgetItem(f"{row['profit_percentage']:.2f}"))
        self.least_products_table.setRowCount(len(df2))
        for i, row in df2.iterrows():
            self.least_products_table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(row['name'])))
            self.least_products_table.setItem(i, 1, QtWidgets.QTableWidgetItem(str(int(row['total_qty']))))
            self.least_products_table.setItem(i, 2, QtWidgets.QTableWidgetItem(f"{row['total_sales']:.2f}"))
            self.least_products_table.setItem(i, 3, QtWidgets.QTableWidgetItem(f"{row['total_profit']:.2f}"))
            self.least_products_table.setItem(i, 4, QtWidgets.QTableWidgetItem(f"{row['profit_percentage']:.2f}"))

    def _on_top_products_error(self, error):
        QtWidgets.QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل المنتجات:\n{error}')

    def load_alerts(self):
        """
        تحميل التنبيهات الذكية في Thread منفصل لمنع التجميد.
        """
        self.alerts_list.clear()
        self.alerts_list.addItem('جاري التحميل...')
        # إذا كان هناك Thread سابق، أوقفه
        if hasattr(self, '_alerts_thread') and self._alerts_thread.isRunning():
            self._alerts_thread.terminate()
        self._alerts_thread = WorkerThread(self._load_alerts_data)
        self._alerts_thread.result_ready.connect(self._on_alerts_ready)
        self._alerts_thread.error.connect(self._on_alerts_error)
        self._alerts_thread.start()

    def _load_alerts_data(self):
        import sqlite3
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        alerts = []
        # تنبيه المنتجات الراكدة (لم تبع منذ 60 يوم)
        c.execute('''SELECT p.name FROM products p LEFT JOIN sales_items si ON p.id=si.product_id
                     LEFT JOIN sales s ON si.sale_id=s.id
                     GROUP BY p.name HAVING MAX(s.sale_date) IS NULL OR MAX(s.sale_date) < date('now', '-60 day')''')
        for row in c.fetchall():
            alerts.append(f'منتج راكد: {row[0]} (لم يُبع منذ أكثر من شهرين)')
        # تنبيه المنتجات الخاسرة (إجمالي الربح سلبي)
        c.execute('''SELECT p.name, SUM(si.quantity*si.sale_price-si.discount) as total_profit
                     FROM sales_items si JOIN products p ON si.product_id=p.id
                     GROUP BY p.name HAVING total_profit < 0''')
        for row in c.fetchall():
            alerts.append(f'منتج يحقق خسارة: {row[0]} (إجمالي ربح سلبي)')
        conn.close()
        return alerts

    def _on_alerts_ready(self, alerts):
        self.alerts_list.clear()
        if not alerts:
            self.alerts_list.addItem('لا توجد تنبيهات حالياً.')
        else:
            for alert in alerts:
                self.alerts_list.addItem(alert)

    def _on_alerts_error(self, error):
        self.alerts_list.clear()
        self.alerts_list.addItem(f'حدث خطأ أثناء تحميل التنبيهات:\n{error}')

    def load_forecast(self):
        self.forecast_status.setText('جاري التحميل...')
        self._forecast_thread = WorkerThread(self._load_forecast_data)
        self._forecast_thread.result_ready.connect(self._on_forecast_ready)
        self._forecast_thread.error.connect(self._on_forecast_error)
        self._forecast_thread.start()

    def _load_forecast_data(self):
        try:
            from prophet import Prophet
        except ImportError:
            return {'error': 'مكتبة prophet غير مثبتة. يرجى تثبيتها أولاً.'}
        import sqlite3
        import pandas as pd
        conn = sqlite3.connect(database.DB_NAME)
        df = pd.read_sql_query("SELECT sale_date, total FROM sales", conn)
        conn.close()
        if df.empty or len(df) < 10:
            return {'error': 'لا توجد بيانات كافية للتوقع.'}
        df = df.groupby('sale_date').sum().reset_index()
        df = df.rename(columns={'sale_date': 'ds', 'total': 'y'})
        df['ds'] = pd.to_datetime(df['ds'])
        model = Prophet(yearly_seasonality=True, daily_seasonality=False)
        model.fit(df)
        future = model.make_future_dataframe(periods=6, freq='M')
        forecast = model.predict(future)
        return {'df': df, 'forecast': forecast}

    def _on_forecast_ready(self, result):
        if 'error' in result:
            self.forecast_status.setText(result['error'])
            return
        df = result['df']
        forecast = result['forecast']
        fig = self.forecast_canvas.figure
        fig.clear()
        ax = fig.add_subplot(111)
        ax.plot(df['ds'], df['y'], label='المبيعات الفعلية', color='#1976d2')
        ax.plot(forecast['ds'], forecast['yhat'], label='توقع المبيعات', color='#43a047', linestyle='--')
        ax.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'], color='#43a047', alpha=0.2, label='مدى الثقة')
        ax.set_title('توقع المبيعات للأشهر القادمة')
        ax.set_xlabel('التاريخ')
        ax.set_ylabel('المبيعات')
        ax.legend()
        fig.tight_layout()
        self.forecast_canvas.draw()
        if forecast['yhat'].iloc[-1] < df['y'].mean():
            self.forecast_status.setText('تنبيه: هناك انخفاض متوقع في المبيعات القادمة.')
        else:
            self.forecast_status.setText('توقع المبيعات مستقر أو في ارتفاع.')

    def _on_forecast_error(self, error):
        self.forecast_status.setText(f'حدث خطأ أثناء التوقع:\n{error}')

    def load_correlation(self):
        self.corr_table.setRowCount(0)
        self._correlation_thread = WorkerThread(self._load_correlation_data)
        self._correlation_thread.result_ready.connect(self._on_correlation_ready)
        self._correlation_thread.error.connect(self._on_correlation_error)
        self._correlation_thread.start()

    def _load_correlation_data(self):
        import sqlite3
        import pandas as pd
        import seaborn as sns
        conn = sqlite3.connect(database.DB_NAME)
        df = pd.read_sql_query('''SELECT si.quantity, si.sale_price, si.discount, si.quantity*si.sale_price as total, s.total as invoice_total FROM sales_items si JOIN sales s ON si.sale_id=s.id''', conn)
        conn.close()
        if df.empty:
            return {'df': None, 'corr': None}
        corr = df.corr()
        return {'df': df, 'corr': corr}

    def _on_correlation_ready(self, result):
        corr = result['corr']
        if corr is None:
            return
        fig = self.corr_canvas.figure
        fig.clear()
        ax = fig.add_subplot(111)
        import seaborn as sns
        import arabic_reshaper
        from bidi.algorithm import get_display
        # ترجمة رؤوس الأعمدة للعربية إذا رغبت بذلك
        columns_map = {
            'quantity': 'الكمية',
            'sale_price': 'سعر البيع',
            'discount': 'الخصم',
            'total': 'إجمالي المنتج',
            'invoice_total': 'إجمالي الفاتورة',
        }
        columns_ar = [get_display(arabic_reshaper.reshape(columns_map.get(col, col))) for col in corr.columns]
        index_ar = [get_display(arabic_reshaper.reshape(columns_map.get(idx, idx))) for idx in corr.index]
        # رسم خريطة الارتباط مع النص العربي
        sns.heatmap(corr, annot=True, cmap='coolwarm', ax=ax, fmt='.2f',
                    xticklabels=columns_ar, yticklabels=index_ar, annot_kws={"fontsize":12})
        ax.set_title(get_display(arabic_reshaper.reshape('مصفوفة الارتباط بين المتغيرات')), fontsize=15, fontweight='bold', color='#1976d2')
        ax.set_xlabel(get_display(arabic_reshaper.reshape('المتغيرات')), fontsize=13, fontweight='bold')
        ax.set_ylabel(get_display(arabic_reshaper.reshape('المتغيرات')), fontsize=13, fontweight='bold')
        # نص توضيحي أسفل الرسم
        fig.tight_layout(rect=[0, 0.08, 1, 1])
        self.corr_canvas.draw()
        # نص توضيحي بالعربية
        desc = (
            'تحليل الارتباط هو أداة إحصائية تساعدك على فهم العلاقة بين متغيرين أو أكثر من بياناتك المالية أو البيعية.\n'
            'في الجدول أعلاه، كل خلية تمثل قوة العلاقة بين متغيرين (مثل الكمية وسعر البيع).\n'
            '\n'
            'القيم تتراوح بين -1 و+1:\n'
            '• إذا كانت القيمة قريبة من +1 فهذا يعني أن هناك علاقة طردية قوية (كلما زاد أحد المتغيرين زاد الآخر).\n'
            '• إذا كانت القيمة قريبة من -1 فهذا يعني علاقة عكسية قوية (كلما زاد أحد المتغيرين قل الآخر).\n'
            '• إذا كانت القيمة قريبة من 0 فهذا يعني عدم وجود علاقة واضحة بين المتغيرين.\n'
            '\n'
            'مثال عملي:\n'
            '- إذا وجدت أن الارتباط بين "الكمية" و"إجمالي المنتج" مرتفع وإيجابي فهذا طبيعي لأن زيادة الكمية تزيد الإجمالي.\n'
            '- إذا كان الارتباط بين "الخصم" و"إجمالي الفاتورة" سلبي فهذا يعني أن زيادة الخصم تقلل من إجمالي الفاتورة.\n'
            '\n'
            'استخدم هذا التحليل لاكتشاف العلاقات المهمة بين متغيرات نشاطك، مما يساعدك في اتخاذ قرارات أفضل في التسعير، العروض، أو إدارة المخزون.'
        )
        if not hasattr(self, 'corr_desc_label'):
            from PyQt5.QtWidgets import QLabel
            self.corr_desc_label = QLabel()
            self.corr_desc_label.setStyleSheet('color:#1976d2; font-size:12pt; font-weight:bold; margin-top:6px;')
            self.corr_canvas.parent().layout().addWidget(self.corr_desc_label)
        self.corr_desc_label.setText(desc)
        self.corr_table.setRowCount(len(corr))
        self.corr_table.setColumnCount(len(corr.columns))
        # استخدم ar_text لضمان دعم العربية بشكل صحيح في رؤوس الأعمدة والصفوف
        self.corr_table.setHorizontalHeaderLabels([ar_text(label) for label in columns_ar])
        self.corr_table.setVerticalHeaderLabels([ar_text(label) for label in index_ar])
        # ضبط اتجاه رؤوس الأعمدة والصفوف لدعم العربية (RTL)
        header = self.corr_table.horizontalHeader()
        vheader = self.corr_table.verticalHeader()
        for i in range(self.corr_table.columnCount()):
            item = self.corr_table.horizontalHeaderItem(i)
            if item:
                item.setTextAlignment(QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
        for i in range(self.corr_table.rowCount()):
            item = self.corr_table.verticalHeaderItem(i)
            if item:
                item.setTextAlignment(QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
        # تعبئة القيم
        for i, row in enumerate(corr.values):
            for j, val in enumerate(row):
                # استخدم ar_text دائمًا لكل القيم (حتى الأرقام) لضمان عدم عكس النص في QTableWidget
                cell_text = ar_text(f'{val:.2f}')
                cell_item = QtWidgets.QTableWidgetItem(cell_text)
                cell_item.setTextAlignment(QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
                self.corr_table.setItem(i, j, cell_item)

    def _on_correlation_error(self, error):
        QtWidgets.QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحليل الارتباط:\n{error}')

    def load_regression(self):
        self.reg_status.setText('جاري التحميل...')
        self._regression_thread = WorkerThread(self._load_regression_data)
        self._regression_thread.result_ready.connect(self._on_regression_ready)
        self._regression_thread.error.connect(self._on_regression_error)
        self._regression_thread.start()

    def _load_regression_data(self):
        import statsmodels.api as sm
        import sqlite3
        import pandas as pd
        conn = sqlite3.connect(database.DB_NAME)
        df = pd.read_sql_query('''SELECT si.quantity, si.sale_price, si.discount, si.quantity*si.sale_price as total FROM sales_items si''', conn)
        conn.close()
        if df.empty or len(df) < 10:
            return {'error': 'لا توجد بيانات كافية لتحليل الانحدار.'}
        X = df[['sale_price', 'quantity', 'discount']]
        y = df['total']
        X = sm.add_constant(X)
        model = sm.OLS(y, X).fit()
        params = model.params
        summary = model.summary2().tables[1]
        y_pred = model.predict(X)
        return {'df': df, 'params': params, 'summary': summary, 'y_pred': y_pred, 'model': model}

    def _on_regression_ready(self, result):
        if 'error' in result:
            self.reg_status.setText(result['error'])
            return
        df = result['df']
        params = result['params']
        summary = result['summary']
        y_pred = result['y_pred']
        model = result['model']
        self.reg_results.setRowCount(len(params))
        self.reg_results.setColumnCount(3)
        self.reg_results.setHorizontalHeaderLabels(['المتغير', 'المعامل', 'P-value'])
        for i, var in enumerate(params.index):
            self.reg_results.setItem(i, 0, QtWidgets.QTableWidgetItem(str(var)))
            self.reg_results.setItem(i, 1, QtWidgets.QTableWidgetItem(f'{params[var]:.2f}'))
            self.reg_results.setItem(i, 2, QtWidgets.QTableWidgetItem(f'{summary.loc[var, "P>|t|"]:.4f}'))
        fig = self.reg_canvas.figure
        fig.clear()
        ax = fig.add_subplot(111)
        ax.scatter(df['sale_price'], df['total'], color='#1976d2', label='البيانات الفعلية')
        ax.plot(df['sale_price'], y_pred, color='#43a047', label='خط الانحدار')
        ax.set_xlabel('سعر البيع')
        ax.set_ylabel('إجمالي المبيعات')
        ax.set_title('تحليل الانحدار: تأثير السعر على المبيعات')
        ax.legend()
        fig.tight_layout()
        self.reg_canvas.draw()
        self.reg_status.setText(f'قوة النموذج (R²): {model.rsquared:.2f}')

    def _on_regression_error(self, error):
        self.reg_status.setText(f'حدث خطأ أثناء تحليل الانحدار:\n{error}')

    def load_clustering(self):
        self.cluster_status.setText('جاري التحميل...')
        self._clustering_thread = WorkerThread(self._load_clustering_data)
        self._clustering_thread.result_ready.connect(self._on_clustering_ready)
        self._clustering_thread.error.connect(self._on_clustering_error)
        self._clustering_thread.start()

    def _load_clustering_data(self):
        try:
            from sklearn.cluster import KMeans
        except ImportError:
            return {'error': 'مكتبة scikit-learn غير مثبتة. يرجى تثبيتها أولاً.'}
        import numpy as np
        import sqlite3
        import pandas as pd
        conn = sqlite3.connect(database.DB_NAME)
        df = pd.read_sql_query('''SELECT p.name, SUM(si.quantity) as total_qty, AVG(si.sale_price) as avg_price FROM sales_items si JOIN products p ON si.product_id=p.id GROUP BY p.name HAVING total_qty IS NOT NULL AND avg_price IS NOT NULL''', conn)
        conn.close()
        if df.empty or len(df) < 3:
            return {'error': 'لا توجد بيانات كافية للتحليل العنقودي.'}
        X = df[['total_qty', 'avg_price']].values
        k = 3
        kmeans = KMeans(n_clusters=k, random_state=0)
        labels = kmeans.fit_predict(X)
        df['cluster'] = labels
        return {'df': df, 'k': k}

    def _on_clustering_ready(self, result):
        if 'error' in result:
            self.cluster_status.setText(result['error'])
            return
        df = result['df']
        k = result['k']
        fig = self.cluster_canvas.figure
        fig.clear()
        ax = fig.add_subplot(111)
        colors = ['#1976d2', '#43a047', '#ff9800', '#7b1fa2', '#e53935', '#0097a7', '#c62828']
        # رسم كل مجموعة مع وسم عربي واحترافي
        import arabic_reshaper
        from bidi.algorithm import get_display
        for i in range(k):
            cluster_points = df[df['cluster'] == i]
            label_ar = get_display(arabic_reshaper.reshape(f'المجموعة {i+1}'))
            ax.scatter(cluster_points['total_qty'], cluster_points['avg_price'], label=label_ar, color=colors[i%len(colors)], s=90, alpha=0.85, edgecolors='black', linewidths=1.2)
            # إضافة وسم لكل نقطة باسم المنتج
            for _, row in cluster_points.iterrows():
                pname = get_display(arabic_reshaper.reshape(str(row['name'])))
                ax.annotate(pname, (row['total_qty'], row['avg_price']), fontsize=10, color=colors[i%len(colors)], fontweight='bold', xytext=(5,5), textcoords='offset points', alpha=0.85)
        # إعداد المحاور والنصوص بالعربية
        ax.set_xlabel(get_display(arabic_reshaper.reshape('الكمية المباعة')), fontsize=13, fontweight='bold')
        ax.set_ylabel(get_display(arabic_reshaper.reshape('متوسط سعر البيع')), fontsize=13, fontweight='bold')
        ax.set_title(get_display(arabic_reshaper.reshape('التحليل العنقودي للمنتجات حسب الكمية والسعر')), fontsize=15, fontweight='bold', color='#1976d2')
        ax.grid(True, linestyle='--', alpha=0.3)
        ax.legend(loc='best', fontsize=12)
        fig.tight_layout()
        self.cluster_canvas.draw()
        # وصف تفصيلي احترافي
        desc = 'تم التحليل بنجاح. كل لون يمثل مجموعة منتجات متشابهة بناءً على الكمية المباعة ومتوسط السعر. النقاط المميزة تمثل المنتجات، وكل مجموعة تساعدك في فهم سلوك المنتجات في السوق (منتجات سريعة الدوران، منتجات مرتفعة السعر، إلخ).'
        self.cluster_status.setText(desc)

    def _on_clustering_error(self, error):
        self.cluster_status.setText(f'حدث خطأ أثناء التحليل العنقودي:\n{error}')

    def refresh_forecast_tab(self):
        # إعادة تحميل التوقع عند تغيير البيانات أو الفترة
        self.load_forecast()

    def refresh_all(self):
        self.load_statistics()
        # إعادة تحميل التبويب الحالي فقط (للتحديث السريع)
        if hasattr(self, 'tabs'):
            idx = self.tabs.currentIndex()
            self._loaded_tabs = set()  # إعادة تعيين المحملين
            self.on_tab_changed(idx)

    def showEvent(self, event):
        super().showEvent(event)
        self.refresh_all()

    def print_current_statistics(self):
        """
        توليد HTML للإحصائيات الحالية وطباعتها باستخدام print_invoice.
        """
        from utils.print_utils import print_invoice
        html = self.generate_statistics_html()
        print_invoice(html, self)

    def generate_statistics_html(self):
        """
        توليد HTML للإحصائيات من جدول QTableWidget الرئيسي.
        """
        items_html = ""
        # رؤوس الأعمدة
        headers = [self.table.horizontalHeaderItem(i).text() for i in range(self.table.columnCount())]
        items_html += '<tr>' + ''.join(f'<th>{h}</th>' for h in headers) + '</tr>'
        for row in range(self.table.rowCount()):
            items_html += '<tr>'
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                items_html += f'<td>{item.text() if item else ""}</td>'
            items_html += '</tr>'
        html = f"""
        <html>
        <head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}}</style></head>
        <body dir='rtl'>
        <h2 style='text-align:center'>إحصائيات المبيعات</h2>
        <table>{items_html}</table>
        </body></html>
        """
        return html