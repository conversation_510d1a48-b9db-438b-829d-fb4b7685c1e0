# نافذة المساعد الذكي (AI Assistant)
# تمكن المستخدم من كتابة أوامر أو أسئلة والحصول على إجابات ذكية
# يمكن تطويرها لاحقاً لدمج نماذج ذكاء اصطناعي حقيقية

from PyQt5 import QtWidgets, QtCore
import requests
import os

class AIAssistantWindow(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('المساعد الذكي')
        self.setGeometry(600, 300, 500, 400)
        # تعيين أيقونة البرنامج
        from PyQt5 import QtGui
        from utils.print_utils import get_icon
        icon_path = get_icon('smart.ico')
        if os.path.exists(icon_path):
            self.setWindowIcon(QtGui.QIcon(icon_path))
        self.setup_ui()
        # إعداد مفتاح OpenAI API من متغير بيئة أو ملف نصي (اختياري)
        self.openai_api_key = os.environ.get('OPENAI_API_KEY', '').strip()
        # --- إضافة وضع الروبوت الذكي وقاعدة معرفة للأسئلة الشائعة ---
        self.smart_bot_mode = False  # وضع الروبوت الذكي (افتراضي: متوقف)
        self.faq_knowledge = {
            'كيف أضيف منتج': 'لإضافة منتج جديد، انتقل إلى قسم المنتجات واضغط على زر إضافة منتج، ثم أدخل البيانات المطلوبة واحفظ.',
            'كيف أستخرج تقرير المبيعات': 'يمكنك استخراج تقرير المبيعات من قسم التقارير ثم اختيار تقرير المبيعات وتحديد الفترة المطلوبة.',
            'ما هو أفضل عميل': 'أفضل عميل هو الذي لديه أعلى قيمة مشتريات ويمكنك معرفة ذلك بسؤال: من هم أفضل العملاء؟',
            'كيف أعدل فاتورة': 'لتعديل فاتورة، انتقل إلى قسم الفواتير وابحث عن الفاتورة المطلوبة ثم اضغط على تعديل.'
        }

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout(self)
        self.chat_area = QtWidgets.QTextEdit()
        self.chat_area.setReadOnly(True)
        layout.addWidget(self.chat_area)
        self.input_line = QtWidgets.QLineEdit()
        self.input_line.setPlaceholderText('اكتب سؤالك أو أمرك هنا...')
        self.input_line.returnPressed.connect(self.handle_user_input)
        layout.addWidget(self.input_line)
        self.send_btn = QtWidgets.QPushButton('إرسال')
        self.send_btn.clicked.connect(self.handle_user_input)
        layout.addWidget(self.send_btn)
        # زر لتفعيل/تعطيل وضع الروبوت الذكي
        self.smart_bot_btn = QtWidgets.QPushButton('تفعيل الروبوت الذكي')
        self.smart_bot_btn.setCheckable(True)
        self.smart_bot_btn.toggled.connect(self.toggle_smart_bot)
        layout.addWidget(self.smart_bot_btn)

    def toggle_smart_bot(self, checked):
        self.smart_bot_mode = checked
        if checked:
            self.smart_bot_btn.setText('إيقاف الروبوت الذكي')
            self.chat_area.append('<i>تم تفعيل وضع الروبوت الذكي. يمكنك الآن طرح أي سؤال عام أو محاسبي.</i>')
        else:
            self.smart_bot_btn.setText('تفعيل الروبوت الذكي')
            self.chat_area.append('<i>تم إيقاف وضع الروبوت الذكي. الإجابات ستكون محاسبية فقط.</i>')

    def handle_user_input(self):
        user_text = self.input_line.text().strip()
        if not user_text:
            return
        self.chat_area.append(f'<b>أنت:</b> {user_text}')
        # رد افتراضي (يمكن ربطه لاحقاً بنموذج ذكاء اصطناعي)
        response = self.get_ai_response(user_text)
        self.chat_area.append(f'<b>المساعد:</b> {response}')
        self.input_line.clear()

    def get_ai_response(self, text):
        # إذا توفر مفتاح OpenAI استخدمه، وإلا استخدم الردود الذكية البسيطة
        if self.openai_api_key:
            try:
                return self.ask_openai(text)
            except Exception as e:
                return f'حدث خطأ في الاتصال بالذكاء الاصطناعي: {e}'
        # --- دعم قاعدة المعرفة عند تفعيل وضع الروبوت الذكي ---
        if self.smart_bot_mode:
            for q, a in self.faq_knowledge.items():
                if q in text:
                    return a
        # ذكاء محلي: جلب بيانات حقيقية من النظام
        text = text.strip()
        lower_text = text.lower()
        # منطق بحث ذكي عن الكلمات المفتاحية لأي صياغة
        keywords = {
            'products_low': ['منتجات ناقصة', 'منتجات منخفضة', 'منتجات منتهية', 'منتجات غير متوفرة', 'منتجات ناقص', 'منتج ناقص', 'كمية منخفضة', 'كمية ناقصة'],
            'inventory': ['مخزون', 'كمية', 'رصيد المخزون', 'المخزون'],
            'sales_report': ['تقرير مبيعات', 'تقارير المبيعات', 'مبيعات', 'مبيعات اليوم', 'مبيعات الشهر', 'مبيعات الكلي', 'اجمالي المبيعات'],
            'purchases_report': ['تقرير مشتريات', 'تقارير المشتريات', 'مشتريات', 'مشتريات اليوم', 'مشتريات الشهر'],
            'customers_report': ['تقرير عملاء', 'تقارير العملاء', 'عملاء', 'أفضل العملاء', 'عميل'],
            'suppliers_report': ['تقرير موردين', 'تقارير الموردين', 'موردين', 'مورد'],
            'invoices': ['فاتورة', 'فواتير', 'عدد الفواتير', 'كم فاتورة'],
        }
        for key, words in keywords.items():
            if any(w in text for w in words):
                if key == 'products_low':
                    return 'يمكنك مراجعة قسم المخزون أو سؤال: ما هي المنتجات منخفضة الكمية؟'  # أو استدعاء دالة مخصصة
                if key == 'inventory':
                    return self.get_inventory_report()
                if key == 'sales_report':
                    if 'اليوم' in text:
                        return self.get_sales_today()
                    return self.get_sales_report()
                if key == 'purchases_report':
                    return self.get_purchases_report()
                if key == 'customers_report':
                    return self.get_customers_report()
                if key == 'suppliers_report':
                    return self.get_suppliers_report()
                if key == 'invoices':
                    if 'اليوم' in text:
                        return self.get_invoices_today()
                    return 'يمكنك معرفة عدد الفواتير اليوم أو كل الفواتير عبر سؤال مباشر.'
        # إذا لم يتمكن من فهم السؤال بشكل مباشر، جرب البحث الذكي عن كلمات رئيسية أو جمل مشابهة
        # مثال: دعم الأسئلة العامة أو غير المباشرة
        if any(word in lower_text for word in ['فاتورة', 'فواتير']):
            if 'اليوم' in lower_text:
                return self.get_invoices_today()
            return 'يمكنك معرفة عدد الفواتير اليوم أو كل الفواتير عبر سؤال مباشر.'
        if any(word in lower_text for word in ['مخزون', 'كمية', 'منتجات ناقصة', 'منتجات منتهية']):
            return 'يمكنك مراجعة قسم المخزون أو سؤال: ما هي المنتجات منخفضة الكمية؟'
        if any(word in lower_text for word in ['مساعدة', 'مساعد', 'مساعدة ذكية', 'كيف', 'طريقة']):
            return 'أنا هنا لمساعدتك في أي استفسار محاسبي أو إداري. جرب: كم مبيعات اليوم؟ أو من هم أفضل العملاء؟'
        # دعم الأسئلة عن التاريخ أو الوقت
        if 'تاريخ اليوم' in lower_text or 'ما التاريخ' in lower_text:
            import datetime
            return f'تاريخ اليوم: {datetime.datetime.now().strftime("%Y-%m-%d")}'

        # fallback
        return 'لم أتمكن من فهم سؤالك بدقة. جرب إعادة صياغة السؤال أو اسأل عن المبيعات، العملاء، الموردين، الفواتير، أو المنتجات.'

    def ask_openai(self, prompt):
        """
        إرسال السؤال إلى OpenAI API (GPT-3.5/4) والحصول على الرد.
        """
        url = 'https://api.openai.com/v1/chat/completions'
        headers = {
            'Authorization': f'Bearer {self.openai_api_key}',
            'Content-Type': 'application/json'
        }
        data = {
            'model': 'gpt-3.5-turbo',
            'messages': [
                {"role": "system", "content": "أنت مساعد ذكي لنظام محاسبي مطعمي. أجب باختصار ووضوح وبالعربية."},
                {"role": "user", "content": prompt}
            ],
            'max_tokens': 200,
            'temperature': 0.2
        }
        resp = requests.post(url, headers=headers, json=data, timeout=15)
        if resp.status_code == 200:
            return resp.json()['choices'][0]['message']['content'].strip()
        else:
            return f'خطأ في الاتصال بـ OpenAI: {resp.status_code} - {resp.text}'

    def get_sales_today(self):
        import sqlite3, datetime
        from models import database
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT IFNULL(SUM(total),0) FROM sales WHERE sale_date=?', (today,))
        sales_today = c.fetchone()[0]
        conn.close()
        return f'إجمالي المبيعات اليوم: {sales_today:.2f} ريال.'

    def get_total_sales(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT IFNULL(SUM(total),0) FROM sales')
        total_sales = c.fetchone()[0]
        conn.close()
        return f'إجمالي المبيعات الكلي: {total_sales:.2f} ريال.'

    def get_top_product(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('''SELECT p.name, SUM(si.quantity) as qty FROM sales_items si JOIN products p ON si.product_id=p.id GROUP BY si.product_id ORDER BY qty DESC LIMIT 1''')
        row = c.fetchone()
        conn.close()
        if row:
            return f'أكثر المنتجات مبيعاً هو "{row[0]}" بعدد {row[1]}.'
        return 'لا توجد بيانات كافية عن المنتجات الأكثر مبيعاً.'

    def get_suppliers_balance(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT IFNULL(SUM(balance),0) FROM suppliers')
        balance = c.fetchone()[0]
        conn.close()
        return f'إجمالي رصيد الموردين: {balance:.2f} ريال.'

    def get_top_customers(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('''SELECT c.name, SUM(s.total) as total FROM sales s JOIN customers c ON s.customer_id=c.id GROUP BY s.customer_id ORDER BY total DESC LIMIT 3''')
        rows = c.fetchall()
        conn.close()
        if not rows:
            return 'لا توجد بيانات كافية عن العملاء.'
        result = 'أفضل العملاء:\n'
        for name, total in rows:
            result += f'- {name}: {total:.2f} ريال\n'
        return result.strip()

    def get_profit_analysis(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT IFNULL(SUM(total),0) FROM sales')
        total_sales = c.fetchone()[0]
        c.execute('SELECT IFNULL(SUM(quantity*purchase_price),0) FROM purchases')
        total_purchases = c.fetchone()[0]
        conn.close()
        profit = total_sales - total_purchases
        return f'إجمالي الأرباح: {profit:.2f} ريال.'

    def get_products_report(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*), IFNULL(SUM(quantity),0) FROM products')
        count, total_qty = c.fetchone()
        conn.close()
        return f'عدد المنتجات: {count}\nإجمالي الكمية في النظام: {total_qty}'

    def get_customers_report(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*) FROM customers')
        count = c.fetchone()[0]
        c.execute('SELECT IFNULL(SUM(total),0) FROM sales')
        total_sales = c.fetchone()[0]
        conn.close()
        return f'عدد العملاء: {count}\nإجمالي مشتريات العملاء: {total_sales:.2f} ريال'

    def get_suppliers_report(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*) FROM suppliers')
        count = c.fetchone()[0]
        c.execute('SELECT IFNULL(SUM(balance),0) FROM suppliers')
        total_balance = c.fetchone()[0]
        conn.close()
        return f'عدد الموردين: {count}\nإجمالي رصيد الموردين: {total_balance:.2f} ريال'

    def get_sales_report(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*), IFNULL(SUM(total),0) FROM sales')
        count, total = c.fetchone()
        conn.close()
        return f'عدد فواتير المبيعات: {count}\nإجمالي المبيعات: {total:.2f} ريال'

    def get_purchases_report(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*), IFNULL(SUM(purchase_price*quantity),0) FROM purchases')
        count, total = c.fetchone()
        conn.close()
        return f'عدد عمليات الشراء: {count}\nإجمالي قيمة المشتريات: {total:.2f} ريال'

    def get_inventory_report(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*), IFNULL(SUM(quantity),0) FROM inventory')
        count, total_qty = c.fetchone()
        conn.close()
        return f'عدد أصناف المخزون: {count}\nإجمالي الكمية في المخزون: {total_qty}'

    def get_least_selling_products(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        # جلب أقل 3 منتجات مبيعاً
        c.execute('''SELECT p.name, IFNULL(SUM(si.quantity),0) as qty FROM products p
                     LEFT JOIN sales_items si ON p.id=si.product_id
                     GROUP BY p.id ORDER BY qty ASC LIMIT 3''')
        rows = c.fetchall()
        conn.close()
        if not rows or all(qty == 0 for _, qty in rows):
            return 'لا توجد بيانات كافية عن المنتجات الأقل مبيعاً.'
        result = 'المنتجات الأقل مبيعاً:\n'
        for name, qty in rows:
            result += f'- {name}: {qty} مبيعات\n'
        result += '\nنصيحة: راجع تسعير هذه المنتجات أو قم بعروض ترويجية لزيادة مبيعاتها.'
        return result.strip()

    def get_sales_forecast(self):
        # مثال مبسط: توقع المبيعات بناءً على متوسط آخر 7 أيام
        import sqlite3, datetime
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        today = datetime.datetime.now().date()
        week_ago = today - datetime.timedelta(days=7)
        c.execute('SELECT IFNULL(SUM(total),0) FROM sales WHERE sale_date >= ?', (week_ago.strftime('%Y-%m-%d'),))
        week_sales = c.fetchone()[0]
        avg_per_day = week_sales / 7 if week_sales else 0
        conn.close()
        return f'توقع مبيعات الغد (تقديري): {avg_per_day:.2f} ريال بناءً على متوسط الأسبوع الماضي.'

    def get_customers_count(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*) FROM customers')
        count = c.fetchone()[0]
        conn.close()
        return f'عدد العملاء المسجلين: {count}'

    def get_suppliers_count(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*) FROM suppliers')
        count = c.fetchone()[0]
        conn.close()
        return f'عدد الموردين المسجلين: {count}'

    def get_products_count(self):
        import sqlite3
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*) FROM products')
        count = c.fetchone()[0]
        conn.close()
        return f'عدد المنتجات المسجلة: {count}'

    def get_invoices_today(self):
        import sqlite3, datetime
        from models import database
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT COUNT(*) FROM sales WHERE sale_date=?', (today,))
        count = c.fetchone()[0]
        conn.close()
        return f'عدد فواتير اليوم: {count}'
