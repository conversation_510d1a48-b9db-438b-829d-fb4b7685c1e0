/*
    ملف تنسيقات QSS لتصميم واجهة البرنامج بشكل عصري وجذاب.
    كل جزء من الكود أدناه يحتوي على تعليق يشرح وظيفته.
*/

/* تحسين مظهر البطاقات والأزرار والخطوط ليكون مناسبًا وواضحًا في جميع السمات */
QWidget {
    font-family: 'Cairo', '<PERSON><PERSON>', 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
    font-size: 13pt;
    color: #222;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e3f0ff, stop:1 #f6f8fa);
}

/* خلفية عامة وخط أصغر وأكثر توازنًا */
QWidget {
    background-image: url("background.png");
    background-repeat: no-repeat;
    background-position: center;
    background-attachment: fixed;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e3f0ff, stop:1 #f6f8fa);
    font-family: '<PERSON>', '<PERSON><PERSON>', 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
    font-size: 13pt;
    color: #222;
    font-weight: 400;
    letter-spacing: 0.2px;
}

/* فريم رئيسي بزاوية دائرية وحدود أخف */
QFrame#mainFrame {
    background: #ffffffee;
    border-radius: 16px;
    border: 1.5px solid #1976d2;
    padding: 18px 24px;
}

/* عنوان رئيسي بارز */
QLabel[role="title"] {
    font-size: 22pt;
    font-family: 'Cairo', 'Amiri', 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
    font-weight: bold;
    color: #1976d2;
    letter-spacing: 1px;
    margin-bottom: 14px;
}

/* أزرار عصرية أصغر */
QPushButton {
    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #1976d2, stop:1 #42a5f5);
    color: white;
    border-radius: 10px;
    padding: 8px 22px;
    font-size: 13pt;
    font-weight: 600;
    border: none;
    margin: 8px 10px;
    qproperty-alignment: AlignCenter;
    text-align: center;
    vertical-align: middle;
}
QPushButton:hover {
    background-color: #1565c0;
    color: #fffde7;
}
QPushButton:pressed {
    background-color: #0d47a1;
}
QPushButton[role="danger"] {
    background: #e53935;
    color: #fff;
}
QPushButton[role="success"] {
    background: #43a047;
    color: #fff;
}

/* فريمات وبطاقات ثانوية */
QGroupBox, QFrame[frameShape="StyledPanel"] {
    background: #fff;
    border-radius: 14px;
    border: 1.5px solid #1976d2;
    padding: 14px 18px;
    margin: 8px 0;
}
QGroupBox:title {
    font-size: 15pt;
    font-weight: bold;
    color: #1976d2;
    margin-top: 8px;
}

/* حقول الإدخال أصغر */
QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox {
    font-size: 13pt;
    border-radius: 8px;
    padding: 5px 10px;
    border: 1.2px solid #b0bec5;
    background: #fff;
}

/* جدول البيانات أصغر وأوضح */
QTableWidget, QTableView {
    background: #f8fafc;
    border-radius: 10px;
    font-size: 12pt;
}
QHeaderView::section {
    background: #1976d2;
    color: #fff;
    font-weight: bold;
    font-size: 13pt;
    border-radius: 8px;
    padding: 6px 0;
}
QTableWidget QTableCornerButton::section {
    background: #1976d2;
}

QMessageBox QLabel {
    font-size: 13pt;
    color: #e53935;
}

QLabel, QCheckBox, QRadioButton {
    font-family: 'Cairo', 'Amiri', 'Segoe UI', 'Arial', 'Tahoma', sans-serif;
    font-size: 13pt;
    color: #222;
    font-weight: 400;
    letter-spacing: 0.2px;
}

QScrollBar:vertical, QScrollBar:horizontal {
    background: #e3f0ff;
    border-radius: 6px;
    width: 12px;
    margin: 1px;
}
QScrollBar::handle:vertical, QScrollBar::handle:horizontal {
    background: #90caf9;
    border-radius: 6px;
    min-height: 16px;
}
QScrollBar::add-line, QScrollBar::sub-line {
    background: none;
}

/* --- تحسين بطاقات ملخص الأداء المالي (statCard) --- */
QFrame#statCard, QFrame[objectName="statCard"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #e3f0ff, stop:1 #1976d2);
    border: 2.5px solid #1976d2;
    border-radius: 18px;
    min-width: 210px;
    min-height: 150px;
    max-width: 260px;
    margin: 10px 8px;
    padding: 12px 8px 8px 8px;
    transition: 0.2s;
}
QFrame#statCard QLabel, QFrame[objectName="statCard"] QLabel {
    font-size: 15pt;
    color: #1976d2;
    font-weight: bold;
    letter-spacing: 0.2px;
    qproperty-alignment: AlignCenter;
    margin-top: 6px;
}
QFrame#statCard QLabel[role="value"], QFrame[objectName="statCard"] QLabel[role="value"] {
    font-size: 18pt;
    color: #263238;
    font-weight: bold;
    margin-top: 2px;
}
QFrame#statCard QLabel[role="icon"], QFrame[objectName="statCard"] QLabel[role="icon"] {
    min-width: 72px;
    min-height: 72px;
    max-width: 72px;
    max-height: 72px;
    margin-bottom: 2px;
}

/* زر تسجيل الخروج في الشريط الجانبي */
QPushButton#logoutBtn {
    background: #e53935;
    color: #fff;
    border-radius: 16px;
    font-size: 14pt;
    font-family: Cairo, Tajawal, Arial;
    margin-top: 18px;
    font-weight: bold;
    letter-spacing: 0.5px;
    min-width: 140px;
    max-width: 210px;
    min-height: 44px;
    text-shadow: 0 1px 4px #000A;
    qproperty-alignment: AlignCenter;
    text-align: center;
    vertical-align: middle;
}
QPushButton#logoutBtn:hover {
    background: #c62828;
    color: #fff;
}
