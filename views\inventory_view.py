# نافذة المخزون
# تعرض حالة المنتجات والكميات والتنبيهات
# تاريخ آخر تعديل: 2025-05-09

from PyQt5 import QtWidgets, QtGui, QtCore
import sqlite3
import json
import os
from models import database
from main import get_app_icon

SETTINGS_FILE = 'inventory_settings.json'

def save_expiry_setting(value, unit):
    data = {'near_expiry_value': value, 'near_expiry_unit': unit}
    with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f)

def load_expiry_setting():
    if os.path.exists(SETTINGS_FILE):
        try:
            with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                value = int(data.get('near_expiry_value', 7))
                unit = data.get('near_expiry_unit', 'days')
                return value, unit
        except Exception:
            return 7, 'days'
    return 7, 'days'

class InventoryWindow(QtWidgets.QWidget):
    def __init__(self, username, role, permissions=None):
        super().__init__()
        self.username = username  # اسم المستخدم الحالي
        self.role = role          # دور المستخدم
        # حفظ الصلاحيات كقائمة
        if permissions is None:
            self.permissions = []
        elif isinstance(permissions, str):
            self.permissions = permissions.split(',')
        else:
            self.permissions = permissions
        # السماح لدور المشتريات بالدخول دائماً بدون فحص صلاحية
        if self.role not in ['manager', 'purchaser'] and 'manage_products' not in self.permissions:
            QtWidgets.QMessageBox.critical(self, 'صلاحيات غير كافية', 'ليس لديك صلاحية الوصول لهذه الشاشة.')
            self.close()
            return
        self.setWindowTitle('إدارة المخزون')
        self.setGeometry(450, 200, 600, 400)
        self.setWindowIcon(get_app_icon())
        self.setup_ui()
        self.expiry_value_spin.valueChanged.connect(self.on_expiry_setting_changed)
        self.expiry_unit_combo.currentIndexChanged.connect(self.on_expiry_setting_changed)
        self.load_inventory()

    def setup_ui(self):
        """
        إعداد واجهة المستخدم لجدول المخزون.
        """
        layout = QtWidgets.QVBoxLayout()
        value, unit = load_expiry_setting()
        self.expiry_value_spin = QtWidgets.QSpinBox()
        self.expiry_value_spin.setRange(1, 90)
        self.expiry_value_spin.setValue(value)
        self.expiry_value_spin.setToolTip('عدد الأيام أو الأشهر التي يظهر عندها تنبيه قرب انتهاء الصلاحية')
        self.expiry_unit_combo = QtWidgets.QComboBox()
        self.expiry_unit_combo.addItems(['يوم', 'شهر'])
        self.expiry_unit_combo.setCurrentIndex(0 if unit == 'days' else 1)
        expiry_days_layout = QtWidgets.QHBoxLayout()
        expiry_days_layout.addWidget(QtWidgets.QLabel('تنبيه قرب انتهاء الصلاحية إذا تبقى أقل من:'))
        expiry_days_layout.addWidget(self.expiry_value_spin)
        expiry_days_layout.addWidget(self.expiry_unit_combo)
        expiry_days_layout.addStretch()
        layout.addLayout(expiry_days_layout)
        self.table = QtWidgets.QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['اسم المنتج', 'الكمية المتوفرة', 'الحد الأدنى', 'تاريخ الانتهاء', 'تنبيه'])
        self.table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.table)
        self.edit_expiry_btn = QtWidgets.QPushButton('تعديل تاريخ الانتهاء')
        self.edit_expiry_btn.clicked.connect(self.edit_expiry_date)
        layout.addWidget(self.edit_expiry_btn)
        # زر تقرير المنتجات المنتهية أو القريبة من الانتهاء
        self.report_btn = QtWidgets.QPushButton('تقرير المخزون')
        self.report_btn.setStyleSheet('background-color: #455a64; color: white; font-weight: bold;')
        self.report_btn.clicked.connect(self.show_inventory_report_window)
        layout.addWidget(self.report_btn)
        # زر طباعة تقرير المخزون
        self.print_btn = QtWidgets.QPushButton('طباعة المخزون')
        self.print_btn.clicked.connect(self.print_current_inventory)
        layout.addWidget(self.print_btn)
        self.setLayout(layout)

    def load_inventory(self):
        """
        تحميل بيانات المنتجات والكميات والتنبيه إذا كانت الكمية منخفضة.
        """
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('''SELECT p.name, IFNULL(i.quantity, 0), p.min_quantity, IFNULL(i.expiry_date, '')
                     FROM products p
                     LEFT JOIN inventory i ON p.id = i.product_id
                     ORDER BY p.name''')
        rows = c.fetchall()
        conn.close()
        self.table.setRowCount(len(rows))
        # تحديث قوائم المنتجات في نوافذ المبيعات بعد تحميل المخزون
        for widget in QtWidgets.QApplication.topLevelWidgets():
            if widget.__class__.__name__ == 'SalesWindow':
                if hasattr(widget, 'refresh_products'):
                    widget.refresh_products()
        value = self.expiry_value_spin.value()
        unit = 'days' if self.expiry_unit_combo.currentIndex() == 0 else 'months'
        today = QtCore.QDate.currentDate()
        for row_idx, (name, qty, min_qty, expiry_date) in enumerate(rows):
            self.table.setItem(row_idx, 0, QtWidgets.QTableWidgetItem(str(name)))
            self.table.setItem(row_idx, 1, QtWidgets.QTableWidgetItem(str(qty)))
            self.table.setItem(row_idx, 2, QtWidgets.QTableWidgetItem(str(min_qty)))
            self.table.setItem(row_idx, 3, QtWidgets.QTableWidgetItem(str(expiry_date)))
            alert_item = QtWidgets.QTableWidgetItem()
            expired = False
            near_expiry = False
            if expiry_date:
                try:
                    exp_date = QtCore.QDate.fromString(expiry_date, 'yyyy-MM-dd')
                    if exp_date.isValid():
                        days_to_expiry = today.daysTo(exp_date)
                        if days_to_expiry < 0:
                            expired = True
                        else:
                            if unit == 'days' and days_to_expiry <= value:
                                near_expiry = True
                            elif unit == 'months':
                                months_to_expiry = today.daysTo(exp_date) // 30
                                if months_to_expiry < value or (months_to_expiry == value and today.addMonths(value).daysTo(exp_date) <= 0):
                                    near_expiry = True
                except:
                    pass
            if qty <= min_qty:
                alert_item.setText('⚠️ كمية منخفضة')
                alert_item.setForeground(QtGui.QColor('red'))
                for col in range(5):
                    item = self.table.item(row_idx, col)
                    if item is None:
                        item = QtWidgets.QTableWidgetItem()
                        self.table.setItem(row_idx, col, item)
                    item.setBackground(QtGui.QColor('#ffe6e6'))
            elif expired:
                alert_item.setText('⛔ منتهي الصلاحية')
                alert_item.setForeground(QtGui.QColor('darkRed'))
                for col in range(5):
                    item = self.table.item(row_idx, col)
                    if item is None:
                        item = QtWidgets.QTableWidgetItem()
                        self.table.setItem(row_idx, col, item)
                    item.setBackground(QtGui.QColor('#ffcccc'))
            elif near_expiry:
                alert_item.setText('⚠️ قرب انتهاء الصلاحية')
                alert_item.setForeground(QtGui.QColor('orange'))
                for col in range(5):
                    item = self.table.item(row_idx, col)
                    if item is None:
                        item = QtWidgets.QTableWidgetItem()
                        self.table.setItem(row_idx, col, item)
                    item.setBackground(QtGui.QColor('#fff2cc'))
            else:
                alert_item.setText('-')
            self.table.setItem(row_idx, 4, alert_item)

    def edit_expiry_date(self):
        row = self.table.currentRow()
        if row < 0:
            QtWidgets.QMessageBox.warning(self, 'تنبيه', 'يرجى اختيار منتج أولاً.')
            return
        product_name = self.table.item(row, 0).text()
        current_expiry = self.table.item(row, 3).text()
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle(f'تعديل تاريخ الانتهاء - {product_name}')
        form = QtWidgets.QFormLayout(dialog)
        date_edit = QtWidgets.QDateEdit()
        date_edit.setCalendarPopup(True)
        date_edit.setDisplayFormat('yyyy-MM-dd')
        if current_expiry:
            try:
                date = QtCore.QDate.fromString(current_expiry, 'yyyy-MM-dd')
                if date.isValid():
                    date_edit.setDate(date)
            except:
                pass
        else:
            date_edit.setDate(QtCore.QDate.currentDate())
        form.addRow('تاريخ الانتهاء:', date_edit)
        btns = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel)
        form.addRow(btns)
        btns.accepted.connect(dialog.accept)
        btns.rejected.connect(dialog.reject)
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            new_expiry = date_edit.date().toString('yyyy-MM-dd')
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('SELECT id FROM products WHERE name = ?', (product_name,))
            row_prod = c.fetchone()
            if row_prod:
                product_id = row_prod[0]
                c.execute('UPDATE inventory SET expiry_date = ? WHERE product_id = ?', (new_expiry, product_id))
                conn.commit()
            conn.close()
            self.load_inventory()

    def show_expiry_report(self):
        """
        عرض نافذة بتقرير المنتجات المنتهية أو القريبة من الانتهاء.
        """
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('''SELECT p.name, i.quantity, i.expiry_date FROM inventory i JOIN products p ON i.product_id = p.id WHERE i.expiry_date IS NOT NULL AND i.expiry_date != '' ''')
        rows = c.fetchall()
        conn.close()
        from datetime import datetime
        from dateutil.relativedelta import relativedelta
        today = datetime.today().date()
        value = self.expiry_value_spin.value()
        unit = 'days' if self.expiry_unit_combo.currentIndex() == 0 else 'months'
        expired = []
        near_expiry = []
        for name, qty, expiry in rows:
            try:
                exp_date = datetime.strptime(expiry, '%Y-%m-%d').date()
                if exp_date < today:
                    expired.append((name, qty, expiry))
                else:
                    days_to_expiry = (exp_date - today).days
                    if unit == 'days' and days_to_expiry <= value:
                        near_expiry.append((name, qty, expiry))
                    elif unit == 'months':
                        months_to_expiry = days_to_expiry // 30
                        if months_to_expiry < value or (months_to_expiry == value and (today.replace(day=1) + relativedelta(months=+value)) >= exp_date):
                            near_expiry.append((name, qty, expiry))
            except:
                continue
        msg = ''
        if expired:
            msg += 'منتجات منتهية الصلاحية:\n' + '\n'.join([f"{n} - الكمية: {q} - انتهى في: {e}" for n, q, e in expired]) + '\n\n'
        if near_expiry:
            msg += 'منتجات ستنتهي قريبًا:\n' + '\n'.join([f"{n} - الكمية: {q} - ينتهي في: {e}" for n, q, e in near_expiry])
        if not msg:
            msg = 'لا توجد منتجات منتهية أو قريبة من الانتهاء.'
        QtWidgets.QMessageBox.information(self, 'تقرير الصلاحية', msg)

    def show_inventory_report_window(self):
        """
        عرض نافذة تقارير التغييرات في المخزون.
        """
        report_window = QtWidgets.QDialog(self)
        report_window.setWindowTitle('تقارير التغييرات - المخزون')
        report_window.setGeometry(500, 250, 700, 400)
        layout = QtWidgets.QVBoxLayout()
        label = QtWidgets.QLabel('سجل التغييرات في المخزون:')
        label.setStyleSheet('font-size: 14pt; font-weight: bold; color: #263238;')
        layout.addWidget(label)
        table = QtWidgets.QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(['العملية', 'اسم المنتج', 'الكمية', 'المستخدم', 'التاريخ'])
        table.horizontalHeader().setStretchLastSection(True)
        # جلب البيانات من جدول log إذا كان موجوداً
        try:
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('''SELECT action, product_name, quantity, user, date FROM log WHERE section = ? ORDER BY date DESC LIMIT 50''', ('inventory',))
            rows = c.fetchall()
            conn.close()
            table.setRowCount(len(rows))
            for row_idx, row_data in enumerate(rows):
                for col_idx, value in enumerate(row_data):
                    table.setItem(row_idx, col_idx, QtWidgets.QTableWidgetItem(str(value)))
        except Exception as e:
            table.setRowCount(1)
            table.setItem(0, 0, QtWidgets.QTableWidgetItem('لا يوجد سجل أو قاعدة بيانات التقارير غير مفعلة'))
        layout.addWidget(table)
        close_btn = QtWidgets.QPushButton('إغلاق')
        close_btn.clicked.connect(report_window.close)
        layout.addWidget(close_btn)
        report_window.setLayout(layout)
        report_window.exec_()

    def on_expiry_setting_changed(self):
        value = self.expiry_value_spin.value()
        unit = 'days' if self.expiry_unit_combo.currentIndex() == 0 else 'months'
        save_expiry_setting(value, unit)
        self.load_inventory()

    def print_current_inventory(self):
        from utils.print_utils import print_invoice
        html = self.generate_inventory_html()
        print_invoice(html, self)

    def generate_inventory_html(self):
        items_html = ""
        for row in self.get_inventory_rows():
            items_html += f"<tr>{''.join(f'<td>{cell}</td>' for cell in row)}</tr>"
        html = f"""
        <html><head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}}</style></head><body dir='rtl'>
        <h2 style='text-align:center'>تقرير المخزون</h2>
        <table>{items_html}</table></body></html>"""
        return html

    def get_inventory_rows(self):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('''SELECT p.name, IFNULL(i.quantity, 0), p.min_quantity, IFNULL(i.expiry_date, '')
                     FROM products p
                     LEFT JOIN inventory i ON p.id = i.product_id
                     ORDER BY p.name''')
        rows = c.fetchall()
        conn.close()
        return rows