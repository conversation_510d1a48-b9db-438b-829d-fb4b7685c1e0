# ملف التشغيل الرئيسي للبرنامج
# يقوم بتجهيز بيئة PyQt5 وتشغيل نافذة تسجيل الدخول
# تاريخ آخر تعديل: 2025-05-10

import os
import sys
import json
import traceback
from PyQt5 import QtWidgets, QtGui, QtCore
from PyQt5.QtCore import Qt, QCoreApplication
from utils.utilities import load_settings
from utils.messages import get_message
from models import database


# إعداد متغير البيئة الخاص بمسار إضافات Qt (مطلوب لتشغيل PyQt5 بشكل صحيح مع بعض طرق التوزيع)
if hasattr(sys, 'frozen'):
    os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = os.path.join(sys._MEIPASS, "PyQt5", "Qt5", "plugins", "platforms")
else:
    import PyQt5
    base = os.path.dirname(PyQt5.__file__)
    os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = os.path.join(base, "Qt5", "plugins", "platforms")


# ملفات الإعدادات لكل نشاط
SETTINGS_FILES = {
    'restaurant': 'settings.json',
    'pharmacy': 'settings_pharmacy.json',
    'supermarket': 'settings_supermarket.json',
    'clinic': 'settings_clinic.json',
    # أضف أنشطة أخرى هنا
}

# تحديد النشاط الحالي
selected_activity = 'restaurant'
if os.path.exists('activity.txt'):
    with open('activity.txt', encoding='utf-8') as f:
        selected_activity = f.read().strip()
settings = load_settings(selected_activity)


# كائن مركزي للمسميات (يستخدم في كل النوافذ)
LABELS = {
    'PRODUCT_LABEL': settings.get('product_label', 'منتج'),
    'CUSTOMER_LABEL': settings.get('customer_label', 'عميل'),
    'SUPPLIER_LABEL': settings.get('supplier_label', 'مورد'),
    'BUSINESS_TYPE': settings.get('business_type', 'نشاط'),
    'UNIT_OPTIONS': settings.get('unit_options', []),
    'EXPIRY_ALERT_DAYS': settings.get('expiry_alert_days', 30)
}


# دالة مركزية لعرض الأخطاء الحرجة
def show_fatal_error(exc_type, exc_value, exc_traceback):
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    QtWidgets.QMessageBox.critical(None, get_message('error_unexpected'), f'حدث خطأ غير متوقع:\n{exc_value}\n\nتفاصيل:\n{error_msg}')

sys.excepthook = show_fatal_error


# دالة موحدة لجلب أيقونة البرنامج
def get_app_icon():
    if hasattr(sys, 'frozen'):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.abspath(__file__))
    icon_path = os.path.join(base_path, 'resources', 'icons', 'smart.ico')
    return QtGui.QIcon(icon_path)


# تنبيه في حال عدم وجود مفتاح OpenAI API (لا يؤثر على النص العربي)
if 'OPENAI_API_KEY' not in os.environ or not os.environ['OPENAI_API_KEY'].strip():
    print('تنبيه: لم يتم العثور على مفتاح OpenAI API في متغير البيئة OPENAI_API_KEY. بعض ميزات الذكاء الاصطناعي لن تعمل إلا بعد إضافة المفتاح.')


# نقطة بدء تشغيل البرنامج
if __name__ == "__main__":
    # دعم الاتجاه من اليمين لليسار وخط عربي افتراضي لكل البرنامج
    QtWidgets.QApplication.setLayoutDirection(QtCore.Qt.RightToLeft)
    font = QtGui.QFont("Cairo", 13)
    QtWidgets.QApplication.setFont(font)
    database.init_db()  # تهيئة قاعدة البيانات وإنشاء الجداول إذا لم تكن موجودة
    app = QtWidgets.QApplication.instance() or QtWidgets.QApplication([])
    # تطبيق QSS موحد إذا توفر
    style_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'resources', 'styles', 'app_style.qss')
    if os.path.exists(style_path):
        with open(style_path, encoding='utf-8') as f:
            app.setStyleSheet(f.read())
    # تعيين أيقونة البرنامج في شريط المهام
    icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'resources', 'icons', 'smart.ico')
    if os.path.exists(icon_path):
        app.setWindowIcon(QtGui.QIcon(icon_path))

    # --- نظام التفعيل الزمني ---
    from utils import activation_core
    from views.activation_view import ActivationWindow
    import datetime
    import uuid
    import hashlib
    import base64

    # اسم ملف التفعيل المخفي وغير المتوقع
    ACTIVATION_FILE = '.sysdata'
    HISTORY_FILE = '.activation_history.log'
    today = datetime.date.today()

    # دالة لجلب MAC address
    def get_mac():
        mac = uuid.getnode()
        return ':'.join(['{:02x}'.format((mac >> ele) & 0xff) for ele in range(40, -1, -8)])

    # دالة تشفير بسيطة (Base64 + hash)
    def encrypt_data(data):
        b = data.encode('utf-8')
        return base64.b64encode(hashlib.sha256(b).digest() + b).decode('utf-8')

    def decrypt_data(enc):
        try:
            raw = base64.b64decode(enc.encode('utf-8'))
            return raw[32:].decode('utf-8')
        except Exception:
            return ''

    # تحقق من وجود ملف السجل واقرأ آخر تاريخ تشغيل
    last_run_date = None
    if os.path.exists(HISTORY_FILE):
        try:
            with open(HISTORY_FILE, 'r', encoding='utf-8') as f:
                last_line = f.readlines()[-1].strip()
                last_run_date = datetime.datetime.strptime(last_line.split(',')[0], '%Y-%m-%d').date()
        except Exception:
            last_run_date = None
    # إذا كان التاريخ الحالي أقدم من آخر تشغيل، فهذا تلاعب
    if last_run_date and today < last_run_date:
        QtWidgets.QMessageBox.critical(None, "تلاعب بالتاريخ", "تم اكتشاف تلاعب بتاريخ النظام. يرجى تصحيح التاريخ وإعادة تشغيل البرنامج.")
        sys.exit(1)

    # تحقق من وجود ملف التفعيل
    mac_addr = get_mac()
    activation_valid = False
    if os.path.exists(ACTIVATION_FILE):
        try:
            with open(ACTIVATION_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                dec = decrypt_data(content)
                if dec == mac_addr:
                    activation_valid = True
        except Exception:
            activation_valid = False

    # إذا لم يكن التفعيل صالحًا أو الملف غير مرتبط بالجهاز الحالي
    if not activation_valid or not activation_core.is_period_valid():
        dlg = ActivationWindow()
        if dlg.exec_() != QtWidgets.QDialog.Accepted or not dlg.result or not activation_core.is_period_valid():
            QtWidgets.QMessageBox.critical(None, "إغلاق البرنامج", "لم يتم تفعيل البرنامج. سيتم الإغلاق.")
            sys.exit(1)
        QtWidgets.QApplication.processEvents()
        # بعد التفعيل الناجح، اربط التفعيل بالجهاز الحالي
        with open(ACTIVATION_FILE, 'w', encoding='utf-8') as f:
            f.write(encrypt_data(mac_addr))
        # سجل محاولة التفعيل
        with open(HISTORY_FILE, 'a', encoding='utf-8') as f:
            f.write(f'{today.isoformat()},{mac_addr},activation\n')
    else:
        # سجل التشغيل العادي
        with open(HISTORY_FILE, 'a', encoding='utf-8') as f:
            f.write(f'{today.isoformat()},{mac_addr},run\n')

    from views import login_view
    login_view.run(LABELS=LABELS)
