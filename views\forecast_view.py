# نافذة توقع المبيعات الذكية
# تعتمد على Prophet أو ARIMA لعرض توقعات المبيعات المستقبلية
from PyQt5 import QtWidgets, QtCore, QtGui
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import matplotlib.pyplot as plt
import pandas as pd
import sqlite3
import os
import arabic_reshaper
from bidi.algorithm import get_display
import matplotlib
from main import get_app_icon
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['font.family'] = ['Cairo', 'Amiri', 'Arial', 'Tahoma', 'sans-serif']

def ar_text(text):
    reshaped = arabic_reshaper.reshape(text)
    return get_display(reshaped)

class ForecastWindow(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('توقع المبيعات الذكي')
        self.setGeometry(500, 250, 700, 500)
        self.setWindowIcon(get_app_icon())
        self.setup_ui()
        self.load_and_forecast()

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout(self)
        self.figure = plt.Figure(figsize=(7,4))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        self.status_label = QtWidgets.QLabel('')
        layout.addWidget(self.status_label)

    def load_and_forecast(self):
        # جلب بيانات المبيعات اليومية
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        df = pd.read_sql_query('SELECT sale_date as ds, SUM(total) as y FROM sales GROUP BY sale_date ORDER BY sale_date', conn)
        conn.close()
        if len(df) < 10:
            self.status_label.setText('لا توجد بيانات كافية للتنبؤ.')
            return
        try:
            from prophet import Prophet
            model = Prophet()
            model.fit(df)
            future = model.make_future_dataframe(periods=7)
            forecast = model.predict(future)
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.plot(df['ds'], df['y'], label='المبيعات الفعلية')
            ax.plot(forecast['ds'], forecast['yhat'], label='التوقع')
            ax.fill_between(forecast['ds'], forecast['yhat_lower'], forecast['yhat_upper'], color='gray', alpha=0.2)
            ax.set_title(ar_text('توقع المبيعات للأيام القادمة'), fontsize=16, fontweight='bold')
            ax.set_ylabel(ar_text('القيمة'), fontsize=14)
            ax.set_xlabel(ar_text('التاريخ'), fontsize=14)
            ax.legend(loc='upper right', fontsize=12)
            self.figure.tight_layout()
            self.canvas.draw()
            self.status_label.setText('تم التنبؤ باستخدام Prophet.')
        except ImportError:
            # إذا لم تتوفر Prophet استخدم ARIMA
            try:
                from statsmodels.tsa.arima.model import ARIMA
                df['ds'] = pd.to_datetime(df['ds'])
                df.set_index('ds', inplace=True)
                model = ARIMA(df['y'], order=(2,1,2))
                model_fit = model.fit()
                forecast = model_fit.forecast(steps=7)
                self.figure.clear()
                ax = self.figure.add_subplot(111)
                ax.plot(df.index, df['y'], label='المبيعات الفعلية')
                future_dates = pd.date_range(df.index[-1], periods=8, closed='right')
                ax.plot(future_dates, forecast, label='التوقع (ARIMA)')
                ax.set_title(ar_text('توقع المبيعات للأيام القادمة'), fontsize=16, fontweight='bold')
                ax.set_ylabel(ar_text('القيمة'), fontsize=14)
                ax.set_xlabel(ar_text('التاريخ'), fontsize=14)
                ax.legend(loc='upper right', fontsize=12)
                self.figure.tight_layout()
                self.canvas.draw()
                self.status_label.setText('تم التنبؤ باستخدام ARIMA.')
            except Exception as e:
                self.status_label.setText(f'فشل التنبؤ: {e}')
