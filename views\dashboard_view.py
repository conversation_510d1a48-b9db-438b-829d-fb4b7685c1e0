# نافذة لوحة التحكم الرئيسية للبرنامج
# تعرض إحصائيات، أزرار تنقل، وتخصيص الثيمات
# تاريخ آخر تعديل: 2025-05-10
from PyQt5 import QtWidgets, QtGui, QtCore
from utils.event_bus import event_bus
import sqlite3
from models import database
import datetime
import os
from utils.utilities import load_settings, parse_permissions, get_user_permissions
from main import get_app_icon

class DashboardWindow(QtWidgets.QWidget):
    def __init__(self, username, role, permissions=None, user_id=None, labels=None):
        super().__init__()
        self.setWindowIcon(get_app_icon())
        self.username = username  # اسم المستخدم الحالي
        self.role = role          # دور المستخدم (manager/cashier/purchaser)
        self.user_id = user_id    # معرف المستخدم
        self.permissions = parse_permissions(permissions)
        self.labels = labels or {
            'PRODUCT_LABEL': 'منتج',
            'CUSTOMER_LABEL': 'عميل',
            'SUPPLIER_LABEL': 'مورد',
            'BUSINESS_TYPE': 'نشاط'
        }
        self.settings = load_settings()  # تحميل الإعدادات العامة للنشاط
        # جلب الصلاحيات الفعلية من قاعدة البيانات
        if self.user_id is not None:
            self.effective_permissions = get_user_permissions(self.user_id)
        else:
            self.effective_permissions = []
        # تعريف الألوان والخطوط كثوابت للكلاس
        self.font_family = 'Cairo, Arial, sans-serif'
        self.main_color = '#1976d2'
        self.secondary_text = '#444'
        self.setWindowTitle(f"الواجة الرئسية للنضام ال{self.labels.get('BUSINESS_TYPE', 'النشاط')}")
        self.setGeometry(400, 150, 800, 600)
        self.setup_ui()
        # ربط إشارة البيع لتحديث الإحصائيات تلقائياً
        event_bus.sale_made.connect(self.refresh_dashboard_stats)

    def showEvent(self, event):
        """
        تحديث الإحصائيات تلقائياً عند العودة إلى نافذة لوحة التحكم.
        """
        self.refresh_dashboard_stats()
        super().showEvent(event)

    def refresh_dashboard_stats(self):
        """
        إعادة تحميل ملخص الأداء المالي والإداري وتحديث البطاقات فوراً.
        """
        stats = self.get_dashboard_stats()
        # تحديث القيم في البطاقات مباشرة
        # البحث عن البطاقات حسب العناوين وتحديث النصوص
        for card in getattr(self, 'stat_cards', []):
            title = card.findChild(QtWidgets.QLabel)
            value = card.findChildren(QtWidgets.QLabel)[1] if len(card.findChildren(QtWidgets.QLabel)) > 1 else None
            if not title or not value:
                continue
            if 'إجمالي المبيعات اليوم' in title.text():
                value.setText(f"{stats['sales_today']:.2f} ر.س")
            elif 'عدد الطلبات اليوم' in title.text():
                value.setText(str(stats['orders_today']))
            elif 'منتجات منخفضة' in title.text():
                value.setText(str(stats['low_stock']))
            elif 'إجمالي المبيعات' in title.text():
                value.setText(f"{stats['total_sales']:.2f} ر.س")
            elif 'رأس المال' in title.text():
                value.setText(f"{stats['capital']:.2f} ر.س")
            elif 'صافي الربح' in title.text():
                value.setText(f"{stats['profit_fifo']:.2f} ر.س")

    def setup_ui(self):
        """
        إعداد واجهة المستخدم للوحة التحكم الرئيسية مع شريط جانبي عصري، شعار دائري، شريط بحث، وخطوط Google Fonts.
        """
        self.setStyleSheet("background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e3f0ffCC, stop:1 #1976d2CC);")
        main_layout = QtWidgets.QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        # --- Sidebar ---
        sidebar = QtWidgets.QFrame()
        sidebar.setObjectName('sidebar')
        sidebar.setMinimumWidth(170)
        sidebar.setMaximumWidth(230)
        sidebar.setSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        # لون متدرج داكن من الأحمر الداكن إلى الأزرق الداكن
        sidebar.setStyleSheet('''QFrame#sidebar {background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7b1f1f, stop:0.5 #232526, stop:1 #0f2027); border-top-left-radius: 24px; border-bottom-left-radius: 24px;}''')
        # دعم السحب لتغيير عرض الشريط الجانبي
        sidebar.setMouseTracking(True)
        self._sidebar_resizing = False
        self._sidebar_start_x = 0
        sidebar.installEventFilter(self)
        # إضافة ScrollArea للشريط الجانبي
        sidebar_scroll = QtWidgets.QScrollArea()
        sidebar_scroll.setWidgetResizable(True)
        sidebar_scroll.setFrameShape(QtWidgets.QFrame.NoFrame)
        sidebar_scroll.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        sidebar_scroll.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        sidebar_content = QtWidgets.QWidget()
        sidebar_layout = QtWidgets.QVBoxLayout(sidebar_content)
        sidebar_layout.setAlignment(QtCore.Qt.AlignTop)
        sidebar_layout.setContentsMargins(0, 24, 0, 24)
        # --- Logo دائري ---
        logo_frame = QtWidgets.QFrame()
        logo_frame.setFixedSize(68, 68)
        logo_frame.setStyleSheet('border-radius: 34px; background: white; border: 3px solid #fff; margin-bottom: 8px;')
        logo_layout = QtWidgets.QVBoxLayout(logo_frame)
        logo_layout.setContentsMargins(0, 0, 0, 0)
        logo = QtWidgets.QLabel()
        from utils.print_utils import get_icon
        pixmap = QtGui.QPixmap(get_icon('logo.png'))
        if pixmap.isNull():
            # عرض أيقونة حسب نوع النشاط
            business_type = self.labels.get('BUSINESS_TYPE', '').strip()
            if business_type == 'صيدلية':
                logo.setText('💊')
            else:
                logo.setText('🏪')
            logo.setAlignment(QtCore.Qt.AlignCenter)
            logo.setStyleSheet('font-size: 32pt; color: #1976d2;')
        else:
            logo.setPixmap(pixmap.scaled(54, 54, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
            logo.setAlignment(QtCore.Qt.AlignCenter)
        logo_layout.addWidget(logo)
        sidebar_layout.addWidget(logo_frame, alignment=QtCore.Qt.AlignHCenter)
        # --- اسم المستخدم وصورة رمزية ---
        avatar = QtWidgets.QLabel()
        avatar.setFixedSize(44, 44)
        avatar.setStyleSheet('border-radius: 22px; background: #e3f0ff; border: 2px solid #fff;')
        avatar_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'resources', 'icons', '12.jpg')
        pixmap = QtGui.QPixmap(avatar_path)
        if not pixmap.isNull():
            avatar.setPixmap(pixmap.scaled(40, 40, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
        else:
            avatar.setText('👤')
            avatar.setAlignment(QtCore.Qt.AlignCenter)
            avatar.setStyleSheet('font-size: 22pt; color: #1976d2; border-radius: 22px; background: #e3f0ff; border: 2px solid #fff;')
        sidebar_layout.addWidget(avatar, alignment=QtCore.Qt.AlignHCenter)
        user_label = QtWidgets.QLabel(self.username)
        user_label.setStyleSheet('font-family: Cairo, Tajawal, Arial; font-size: 11pt; color: #fff; font-weight: bold; margin-bottom: 18px;')
        user_label.setAlignment(QtCore.Qt.AlignCenter)
        sidebar_layout.addWidget(user_label)
        # إضافة زر تسجيل الخروج مباشرة بعد اسم المستخدم
        logout_btn = QtWidgets.QPushButton('تسجيل الخروج')
        logout_btn.setObjectName('logoutBtn')  # إضافة objectName لاستخدام QSS
        logout_btn.setToolTip('تسجيل الخروج من النظام')
        logout_btn.setCursor(QtCore.Qt.PointingHandCursor)
        logout_btn.setMinimumHeight(48)
        logout_btn.setMinimumWidth(170)
        logout_btn.setMaximumWidth(230)
        logout_btn.setIcon(QtGui.QIcon(get_icon('logout.png')))
        logout_btn.setIconSize(QtCore.QSize(28, 28))
        logout_btn.setLayoutDirection(QtCore.Qt.RightToLeft)
        # احترافية إضافية: ظل خفيف وحركة
        logout_btn.setStyleSheet('''QPushButton#logoutBtn {background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #e53935, stop:1 #b71c1c); color: #fff; border-radius: 18px; font-size: 15pt; font-family: Cairo, Tajawal, Arial; font-weight: bold; letter-spacing: 0.5px; min-width: 170px; max-width: 230px; min-height: 48px; margin-top: 18px; text-shadow: 0 1px 4px #000A; box-shadow: 0 2px 8px #e5393533; transition: all 0.2s;} QPushButton#logoutBtn:hover {background: #c62828; color: #fff; transform: scale(1.04);}''')
        logout_btn.clicked.connect(self.logout)
        sidebar_layout.addWidget(logout_btn, alignment=QtCore.Qt.AlignHCenter)
        self.logout_btn = logout_btn
        # --- Sidebar Grouping ---
        # تعريف مجموعات الشريط الجانبي
        sidebar_groups = [
            ("العمليات اليومية", [
                ('المشتريات', 'shopping_cart', self.goto_purchases),
                ('المبيعات', 'point_of_sale', self.goto_sales),
                ('المخزون', 'inventory', self.goto_inventory),
                ('إدارة المنتجات المتقدمة', 'inventory_2', self.goto_products),
            ]),
            ("تحليلات وتقارير", [
                ('التقارير', 'bar_chart', self.goto_reports),
                ('الإحصائيات', 'insights', self.goto_statistics),
                ('توقع المبيعات', 'trending_up', self.open_forecast),
                ('تحليل العملاء', 'groups', self.open_customer_segmentation),
                ('تحليل المنتجات', 'insights', self.open_product_analysis),
            ]),
            ("إدارة و دعم", [
                ('العملاء', 'groups', self.goto_customers),
                ('الموردين', 'local_shipping', self.goto_suppliers),
                ('الإعدادات', 'settings', self.goto_settings),
                ('المساعد الذكي', 'smart_toy', self.open_ai_assistant),
                ('دليل المستخدم', 'help', self.show_user_guide),
            ])
        ]
        # إضافة الأزرار مع العناوين والفواصل مع فحص الصلاحيات
        from utils.utilities import has_permission
        btn_widgets = []
        for group_idx, (group_name, btns) in enumerate(sidebar_groups):
            group_label = QtWidgets.QLabel(group_name)
            group_label.setStyleSheet('font-size: 13pt; color: #111; font-family: Cairo, Tajawal, Arial; font-weight: bold; margin: 18px 0 6px 0; padding-right: 12px; text-shadow: 0 1px 4px #fffA;')
            group_label.setAlignment(QtCore.Qt.AlignRight)
            sidebar_layout.addWidget(group_label)
            for name, icon_name, slot in btns:
                # منع تكرار زر تسجيل الخروج ضمن المجموعات
                if name == 'تسجيل الخروج':
                    continue
                # تحديد اسم الصلاحية المطلوب لكل زر (يمكنك التخصيص حسب الحاجة)
                perm_map = {
                    'المشتريات': 'purchases',
                    'المبيعات': 'sales',
                    # السماح لدور المشتريات بالدخول للمخزون
                    'المخزون': 'manage_products' if self.role != 'purchaser' else None,
                    'إدارة المنتجات المتقدمة': 'manage_products',
                    'التقارير': 'view_reports',
                    'الإحصائيات': 'view_statistics',
                    'توقع المبيعات': 'view_statistics',
                    'تحليل العملاء': 'view_reports',
                    'تحليل المنتجات': 'product_analysis',
                    'العملاء': 'manage_customers',
                    'الموردين': 'manage_suppliers',
                    'الإعدادات': 'edit',
                    'المساعد الذكي': 'view_reports',
                    'دليل المستخدم': None,
                }
                perm_required = perm_map.get(name)
                btn = QtWidgets.QPushButton()
                btn.setToolTip(name)
                btn.setCursor(QtCore.Qt.PointingHandCursor)
                btn.setMinimumHeight(44)
                btn.setMinimumWidth(140)
                btn.setMaximumWidth(210)
                btn.setText(name)
                icon_path = get_icon(f'{icon_name}.png')
                if not QtGui.QPixmap(icon_path).isNull():
                    btn.setIcon(QtGui.QIcon(icon_path))
                    btn.setIconSize(QtCore.QSize(26, 26))
                    btn.setLayoutDirection(QtCore.Qt.RightToLeft)
                # إذا كان الدور كاشير وزر المبيعات، فعّل الزر دائماً
                if name == 'المبيعات' and self.role == 'cashier':
                    btn.setStyleSheet('''QPushButton {background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e53935, stop:1 #1976d2); border-radius: 16px; margin: 6px 0; font-size: 14pt; color: #fff; text-align: right; padding-right: 18px; font-family: Cairo, Tajawal, Arial; font-weight: bold; letter-spacing: 0.5px; text-shadow: 0 1px 4px #000A; transition: all 0.2s;} QPushButton:hover {background: #fff; color: #1976d2; font-weight: bold;}''')
                    btn.clicked.connect(slot)
                # إذا كان الدور مشتريات وزر المشتريات، فعّل الزر دائماً
                elif name == 'المشتريات' and self.role == 'purchaser':
                    btn.setStyleSheet('''QPushButton {background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e53935, stop:1 #1976d2); border-radius: 16px; margin: 6px 0; font-size: 14pt; color: #fff; text-align: right; padding-right: 18px; font-family: Cairo, Tajawal, Arial; font-weight: bold; letter-spacing: 0.5px; text-shadow: 0 1px 4px #000A; transition: all 0.2s;} QPushButton:hover {background: #fff; color: #1976d2; font-weight: bold;}''')
                    btn.clicked.connect(slot)
                # باقي الأزرار بنفس المنطق القديم
                elif perm_required and not has_permission(self.role, self.permissions, perm_required):
                    btn.setEnabled(False)
                    btn.setStyleSheet('''QPushButton {background: #bbb; color: #eee; border-radius: 16px; margin: 6px 0; font-size: 14pt; text-align: right; padding-right: 18px; font-family: Cairo, Tajawal, Arial; font-weight: bold; letter-spacing: 0.5px; opacity:0.5;} QPushButton:hover {background: #bbb; color: #eee;}''')
                    shadow = QtWidgets.QGraphicsDropShadowEffect()
                    shadow.setBlurRadius(18)
                    shadow.setColor(QtGui.QColor(80, 80, 80, 120))
                    shadow.setOffset(0, 4)
                    btn.setGraphicsEffect(shadow)
                    btn.setToolTip(f"غير متاح: تحتاج صلاحية '{perm_required}'")
                else:
                    btn.setStyleSheet('''QPushButton {background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e53935, stop:1 #1976d2); border-radius: 16px; margin: 6px 0; font-size: 14pt; color: #fff; text-align: right; padding-right: 18px; font-family: Cairo, Tajawal, Arial; font-weight: bold; letter-spacing: 0.5px; text-shadow: 0 1px 4px #000A; transition: all 0.2s;} QPushButton:hover {background: #fff; color: #1976d2; font-weight: bold;}''')
                    btn.clicked.connect(slot)
                sidebar_layout.addWidget(btn, alignment=QtCore.Qt.AlignHCenter)
                btn_widgets.append(btn)
            # فاصل بين المجموعات (إلا آخر مجموعة)
            if group_idx < len(sidebar_groups) - 1:
                line = QtWidgets.QFrame()
                line.setFrameShape(QtWidgets.QFrame.HLine)
                line.setFrameShadow(QtWidgets.QFrame.Sunken)
                line.setStyleSheet('color: #e3f0ff; background: #e3f0ff; min-height: 2px;')
                sidebar_layout.addWidget(line)
        # إزالة إضافة زر تسجيل الخروج من الأسفل
        self.sidebar_btns = btn_widgets
        sidebar_layout.addStretch()
        sidebar_scroll.setWidget(sidebar_content)
        sidebar_layout_outer = QtWidgets.QVBoxLayout(sidebar)
        sidebar_layout_outer.setContentsMargins(0, 0, 0, 0)
        sidebar_layout_outer.addWidget(sidebar_scroll)
        main_layout.addWidget(sidebar)
        # --- Main Content ---
        content_widget = QtWidgets.QWidget()
        content_layout = QtWidgets.QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        # --- شريط البحث ---
        search_bar = QtWidgets.QLineEdit()
        search_bar.setPlaceholderText('ابحث عن ميزة أو منتج...')
        search_bar.setFixedHeight(38)
        search_bar.setStyleSheet('font-size: 14pt; border-radius: 14px; padding: 0 18px; background: #fff; color: #1976d2; font-family: Cairo, Tajawal, Arial; margin: 18px 24px 0 24px;')
        content_layout.addWidget(search_bar)
        # --- زر تبديل الوضع الليلي ---
        self.dark_mode_btn = QtWidgets.QPushButton()
        self.dark_mode_btn.setCheckable(True)
        self.dark_mode_btn.setFixedSize(44, 44)
        self.dark_mode_btn.setIcon(QtGui.QIcon(get_icon('moon.png')))
        self.dark_mode_btn.setIconSize(QtCore.QSize(28, 28))
        self.dark_mode_btn.setToolTip('تبديل الوضع الليلي/النهاري')
        self.dark_mode_btn.setStyleSheet('''QPushButton {background: #fff; border-radius: 22px; color: #1976d2; font-size: 15pt; margin: 0 12px;} QPushButton:checked {background: #263238; color: #fff;}''')
        self.dark_mode_btn.clicked.connect(self.toggle_theme)
        # --- العنوان الرئيسي ---
        title = QtWidgets.QLabel('نضام محاسبي ذكي - لوحة التحكم')
        title.setProperty('role', 'title')
        title.setAlignment(QtCore.Qt.AlignVCenter | QtCore.Qt.AlignRight)
        title.setStyleSheet(f'font-family: Cairo, Tajawal, Arial; font-size: 22pt; color: {self.main_color}; font-weight: bold; margin: 12px 24px 0 0;')
        shadow = QtWidgets.QGraphicsDropShadowEffect()
        shadow.setBlurRadius(16)
        shadow.setColor(QtGui.QColor(25, 118, 210, 80))
        shadow.setOffset(0, 4)
        title.setGraphicsEffect(shadow)
        # --- شريط علوي جديد ---
        topbar_layout = QtWidgets.QHBoxLayout()
        topbar_layout.setContentsMargins(0, 0, 0, 0)
        topbar_layout.setSpacing(0)
        # زر تبديل الوضع الليلي
        self.dark_mode_btn = QtWidgets.QPushButton()
        self.dark_mode_btn.setCheckable(True)
        self.dark_mode_btn.setFixedSize(44, 44)
        self.dark_mode_btn.setIcon(QtGui.QIcon('resources/icons/moon.png'))
        self.dark_mode_btn.setIconSize(QtCore.QSize(28, 28))
        self.dark_mode_btn.setToolTip('تبديل الوضع الليلي/النهاري')
        self.dark_mode_btn.setStyleSheet('''QPushButton {background: #fff; border-radius: 22px; color: #1976d2; font-size: 15pt; margin: 0 12px;} QPushButton:checked {background: #263238; color: #fff;}''')
        self.dark_mode_btn.clicked.connect(self.toggle_theme)
        # زر اختيار الثيمات
        self.theme_combo = QtWidgets.QComboBox()
        self.themes = [
            ("أزرق فاتح عصري", "light_blue.xml"),
            ("أزرق داكن عصري", "dark_blue.xml"),
            ("سماوي فاتح", "light_cyan_500.xml"),
            ("بنفسجي فاتح", "light_purple.xml"),
            ("بنفسجي داكن", "dark_purple.xml")
        ]
        for name, _ in self.themes:
            self.theme_combo.addItem(name)
        self.theme_combo.setCurrentIndex(3)  # البنفسجي الفاتح
        self.theme_combo.setFixedWidth(160)
        self.theme_combo.setStyleSheet('font-size: 13pt; padding: 6px 12px; border-radius: 10px; background: #e3f0ff; color: #1976d2; font-family: Cairo, Tajawal, Arial; margin-right: 8px;')
        self.theme_combo.currentIndexChanged.connect(self.change_material_theme)
        # تطبيق الثيم البنفسجي الفاتح مباشرة عند بدء التشغيل
        from qt_material import apply_stylesheet
        app = QtWidgets.QApplication.instance()
        try:
            apply_stylesheet(app, theme="light_purple.xml")
        except Exception:
            pass
        # زر ضم الثيمات
        self.reset_theme_btn = QtWidgets.QPushButton('ضم الثيمات')
        self.reset_theme_btn.setToolTip('إرجاع الثيم إلى الافتراضي (أزرق فاتح عصري)')
        self.reset_theme_btn.setStyleSheet('font-size: 12pt; background: #fff; color: #1976d2; border-radius: 12px; padding: 4px 16px;')
        self.reset_theme_btn.clicked.connect(self.apply_default_theme)
        topbar_layout.addWidget(self.reset_theme_btn)
        # إضافة الأزرار للشريط العلوي
        topbar_layout.addWidget(self.dark_mode_btn)
        topbar_layout.addWidget(self.theme_combo)
        topbar_layout.addStretch()
        topbar_layout.addWidget(title)
        # إزالة أي insertLayout مكرر
        content_layout.insertLayout(1, topbar_layout)
        # --- ملخص سريع (Quick Stats) ---
        stats_title = QtWidgets.QLabel('ملخص الأداء المالي والإداري')
        stats_title.setAlignment(QtCore.Qt.AlignRight)
        stats_title.setStyleSheet('font-size: 16pt; color: #0d47a1; font-family: Cairo, Amiri, Segoe UI, Arial, Tahoma, sans-serif; font-weight: 900; margin: 18px 0 8px 0; text-shadow: 1px 1px 2px #b0bec5;')
        content_layout.addWidget(stats_title)
        stats_grid = QtWidgets.QGridLayout()
        stats_grid.setHorizontalSpacing(32)
        stats_grid.setVerticalSpacing(18)
        stats = self.get_dashboard_stats()
        # إنشاء البطاقات وتخزينها في قائمة stat_cards
        self.stat_cards = []
        card_total_sales = self.create_stat_card('إجمالي المبيعات', f"{stats['total_sales']:.2f} ر.س", 'resources/icons/sales.png')
        card_profit = self.create_stat_card('صافي الربح (FIFO)', f"{stats['profit_fifo']:.2f} ر.س", 'resources/icons/profit.png')
        card_profit.setStyleSheet('''QFrame#statCard {background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #e8f5e9, stop:1 #43a047); border: 2.5px solid #43a047; border-radius: 18px; transition: 0.2s;} QFrame#statCard:hover {box-shadow: 0 0 24px #43a047AA; background: #e8f5e9;}''')
        card_profit.setToolTip('صافي الربح محسوب بطريقة FIFO (الوارد أولاً يخرج أولاً) بناءً على تكلفة البضاعة المباعة.')
        card_capital = self.create_stat_card('رأس المال (المشتريات)', f"{stats['capital']:.2f} ر.س", 'resources/icons/purchase.png')
        card_profit.setToolTip('صافي الربح محسوب بطريقة FIFO (الوارد أولاً يخرج أولاً) بناءً على تكلفة البضاعة المباعة.')
        stats_grid.addWidget(card_total_sales, 0, 0)
        stats_grid.addWidget(card_profit, 0, 1)
        stats_grid.addWidget(card_capital, 0, 2)
        card1 = self.create_stat_card('إجمالي المبيعات اليوم', f"{stats['sales_today']:.2f} ر.س", 'resources/icons/sales.png')
        card2 = self.create_stat_card('عدد الطلبات اليوم', str(stats['orders_today']), 'resources/icons/purchase.png')
        card3 = self.create_stat_card('منتجات منخفضة', str(stats['low_stock']), 'resources/icons/inventory.png')
        stats_grid.addWidget(card1, 1, 0)
        stats_grid.addWidget(card2, 1, 1)
        stats_grid.addWidget(card3, 1, 2)
        self.stat_cards = [card_total_sales, card_profit, card_capital, card1, card2, card3]
        stats_grid.setColumnStretch(0, 1)
        stats_grid.setColumnStretch(1, 1)
        stats_grid.setColumnStretch(2, 1)
        content_layout.addLayout(stats_grid)
        # إزالة الأزرار الرئيسية من منتصف الصفحة (حذف كود main_frame و btn_layout)
        # Footer
        footer = QtWidgets.QLabel('جميع الحقوق محفوظة © النضام 2025')
        footer.setAlignment(QtCore.Qt.AlignCenter)
        footer.setStyleSheet(f'color: #888; font-size: 12pt; margin: 12px; font-family: {self.font_family};')
        content_layout.addWidget(footer)
        main_layout.addWidget(content_widget)
        self.setLayout(main_layout)
        self.setMinimumSize(900, 650)
        self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

    def create_stat_card(self, title, value, icon_path):
        card = QtWidgets.QFrame()
        card.setObjectName('statCard')
        card.setMinimumSize(210, 150)  # تكبير حجم البطاقة
        card.setMaximumWidth(260)
        layout = QtWidgets.QVBoxLayout(card)
        layout.setAlignment(QtCore.Qt.AlignCenter)
        icon = QtWidgets.QLabel()
        icon.setProperty('role', 'icon')
        # تكبير الأيقونة
        if QtGui.QPixmap(icon_path).isNull() is False:
            icon.setPixmap(QtGui.QPixmap(icon_path).scaled(72, 72, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
        icon.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(icon)
        label = QtWidgets.QLabel(title)
        label.setAlignment(QtCore.Qt.AlignCenter)
        label.setStyleSheet('font-size: 15pt; font-weight: bold; margin-top: 6px;')
        layout.addWidget(label)
        val = QtWidgets.QLabel(value)
        val.setProperty('role', 'value')
        val.setAlignment(QtCore.Qt.AlignCenter)
        val.setStyleSheet('font-size: 18pt; font-weight: bold; color: #263238; margin-top: 2px;')
        layout.addWidget(val)
        # تلوين البطاقة حسب العنوان
        if 'إجمالي المبيعات اليوم' in title or 'إجمالي المبيعات' in title or 'إجمالي الربح' in title:
            card.setStyleSheet('''QFrame#statCard {background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #e3f0ff, stop:1 #1976d2); border: 2.5px solid #1976d2; border-radius: 18px; transition: 0.2s;} QFrame#statCard:hover {box-shadow: 0 0 24px #1976d2AA; background: #e3f0ff;}''')
        elif 'رأس المال' in title:
            card.setStyleSheet('''QFrame#statCard {background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #fff3e0, stop:1 #ff9800); border: 2.5px solid #ff9800; border-radius: 18px; transition: 0.2s;} QFrame#statCard:hover {box-shadow: 0 0 24px #ff9800AA; background: #fff3e0;}''')
        elif 'منتجات منخفضة' in title:
            card.setStyleSheet('''QFrame#statCard {background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #ffebee, stop:1 #e53935); border: 2.5px solid #e53935; border-radius: 18px; transition: 0.2s;} QFrame#statCard:hover {box-shadow: 0 0 24px #e53935AA; background: #ffebee;}''')
        elif 'عدد الطلبات' in title:
            card.setStyleSheet('''QFrame#statCard {background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #e8f5e9, stop:1 #43a047); border: 2.5px solid #43a047; border-radius: 18px; transition: 0.2s;} QFrame#statCard:hover {box-shadow: 0 0 24px #43a047AA; background: #e8f5e9;}''')
        return card

    def get_dashboard_stats(self):
        """
        جلب إحصائيات اليوم (إجمالي المبيعات، عدد الطلبات، المنتجات منخفضة الكمية، إجمالي المبيعات الكلي، رأس المال).
        الربح الفعلي يُحسب بطريقة FIFO (تكلفة البضاعة المباعة).
        """
        import pandas as pd
        from utils.utilities import get_profit_fifo
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        c.execute('SELECT IFNULL(SUM(total),0) FROM sales WHERE sale_date=?', (today,))
        sales_today = c.fetchone()[0]
        c.execute('SELECT COUNT(*) FROM sales WHERE sale_date=?', (today,))
        orders_today = c.fetchone()[0]
        c.execute('SELECT COUNT(*) FROM products p LEFT JOIN inventory i ON p.id=i.product_id WHERE IFNULL(i.quantity,0) <= p.min_quantity')
        low_stock = c.fetchone()[0]
        # إجمالي المبيعات الكلي
        c.execute('SELECT IFNULL(SUM(total),0) FROM sales')
        total_sales = c.fetchone()[0]
        # رأس المال (إجمالي تكلفة الشراء)
        c.execute('SELECT IFNULL(SUM(quantity*purchase_price),0) FROM purchases')
        capital = c.fetchone()[0]
        # الربح بطريقة FIFO
        # جلب بيانات المبيعات لكل منتج
        sales_df = pd.read_sql_query('SELECT product_id, SUM(quantity) as qty, SUM(sale_price * quantity) as sales FROM sales_items GROUP BY product_id', conn)
        profit_fifo = 0.0
        try:
            profit_fifo = get_profit_fifo(sales_df, database.DB_NAME)
        except Exception:
            profit_fifo = 0.0
        conn.close()
        return {
            'sales_today': sales_today or 0,
            'orders_today': orders_today or 0,
            'low_stock': low_stock or 0,
            'total_sales': total_sales or 0,
            'capital': capital or 0,
            'profit_fifo': profit_fifo or 0
        }

    def goto_purchases(self):
        """
        فتح نافذة المشتريات.
        """
        from views.purchase_view import PurchaseWindow
        self.purchase_window = PurchaseWindow()
        self.purchase_window.show()
    def goto_sales(self):
        """
        فتح نافذة المبيعات.
        """
        from views.sales_view import SalesWindow
        self.sales_window = SalesWindow()
        self.sales_window.show()
    def goto_inventory(self):
        """
        فتح نافذة المخزون. السماح لدور المشتريات بالدخول دائماً بدون فحص صلاحية.
        """
        from views.inventory_view import InventoryWindow
        # إذا كان الدور مشتريات، السماح بالدخول دائماً
        if self.role == 'purchaser':
            self.inventory_window = InventoryWindow(self.username, self.role)
            self.inventory_window.show()
        else:
            self.inventory_window = InventoryWindow(self.username, self.role)
            self.inventory_window.show()
    def goto_reports(self):
        """
        فتح نافذة التقارير.
        """
        from views.reports_view import ReportsWindow
        self.reports_window = ReportsWindow(self.username, self.role)
        self.reports_window.show()
    def goto_settings(self):
        """
        فتح نافذة الإعدادات.
        """
        from views.settings_view import SettingsWindow
        self.settings_window = SettingsWindow(self.username, self.role)
        self.settings_window.show()
    def goto_statistics(self):
        """
        فتح نافذة الإحصائيات.
        """
        from views.statistics_view import StatisticsWindow
        self.statistics_window = StatisticsWindow(self.username, self.role)
        self.statistics_window.show()
    def goto_customers(self):
        """
        فتح نافذة العملاء.
        """
        from views.customer_view import CustomerWindow
        self.customer_window = CustomerWindow(self.effective_permissions)
        self.customer_window.show()
    def goto_suppliers(self):
        """
        فتح نافذة الموردين.
        """
        from views.supplier_view import SupplierWindow
        self.supplier_window = SupplierWindow(self.effective_permissions)
        self.supplier_window.show()
    def goto_products(self):
        """
        فتح نافذة إدارة المنتجات المتقدمة.
        """
        from views.product_view import ProductWindow
        self.product_window = ProductWindow(self.permissions, labels=self.labels)
        self.product_window.show()

    def toggle_theme(self):
        """
        تبديل بين الوضع الليلي (QDarkStyle) والوضع النهاري (QSS مخصص)
        """
        import qdarkstyle
        app = QtWidgets.QApplication.instance()
        if self.dark_mode_btn.isChecked():
            app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt5())
            from utils.print_utils import get_icon
            self.dark_mode_btn.setIcon(QtGui.QIcon(get_icon('sun.png')))
            self.dark_mode_btn.setToolTip('الوضع النهاري')
        else:
            try:
                with open('resources/styles/app_style.qss', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
            except Exception:
                app.setStyleSheet('')
            from utils.print_utils import get_icon
            self.dark_mode_btn.setIcon(QtGui.QIcon(get_icon('moon.png')))
            self.dark_mode_btn.setToolTip('الوضع الليلي')

    def change_material_theme(self, idx):
        """
        تغيير الثيم بناءً على الاختيار من القائمة.
        """
        from qt_material import apply_stylesheet
        theme_file = self.themes[idx][1]
        app = QtWidgets.QApplication.instance()
        try:
            apply_stylesheet(app, theme=theme_file)
        except Exception:
            pass

    def apply_default_theme(self):
        """
        عند الضغط على زر "ضم الثيمات" يتم تطبيق الثيم الأصلي المخصص للبرنامج (app_style.qss)
        """
        app = QtWidgets.QApplication.instance()
        try:
            with open('resources/styles/app_style.qss', encoding='utf-8') as f:
                app.setStyleSheet(f.read())
        except Exception:
            app.setStyleSheet("")
        self.theme_combo.setCurrentIndex(0)

    def open_ai_assistant(self):
        from views.ai_assistant import AIAssistantWindow
        self.ai_window = AIAssistantWindow()
        self.ai_window.show()

    def open_forecast(self):
        from views.forecast_view import ForecastWindow
        self.forecast_window = ForecastWindow()
        self.forecast_window.show()

    def open_customer_segmentation(self):
        from views.customer_segmentation_view import CustomerSegmentationWindow
        self.segmentation_window = CustomerSegmentationWindow()
        self.segmentation_window.show()

    def open_product_analysis(self):
        from views.reports_view import ReportsWindow
        self.reports_window = ReportsWindow(self.username, self.role)
        self.reports_window.show_products_analysis()

    def eventFilter(self, obj, event):
        # دعم سحب الشريط الجانبي لتغيير عرضه
        if obj.objectName() == 'sidebar':
            if event.type() == QtCore.QEvent.MouseButtonPress and event.button() == QtCore.Qt.LeftButton:
                if abs(event.x() - obj.width()) < 12:
                    self._sidebar_resizing = True
                    self._sidebar_start_x = event.globalX()
                    self._sidebar_start_width = obj.width()
                    return True
            elif event.type() == QtCore.QEvent.MouseMove and self._sidebar_resizing:
                delta = event.globalX() - self._sidebar_start_x
                new_width = max(60, min(320, self._sidebar_start_width + delta))
                obj.setMinimumWidth(new_width)
                obj.setMaximumWidth(new_width)
                return True
            elif event.type() == QtCore.QEvent.MouseButtonRelease and self._sidebar_resizing:
                self._sidebar_resizing = False
                return True
            return super().eventFilter(obj, event)

    def logout(self):
        """
        تسجيل الخروج: إغلاق جميع النوافذ والخروج من البرنامج فوراً.
        """
        from PyQt5.QtWidgets import QApplication
        # إغلاق جميع النوافذ الفرعية إذا كانت موجودة
        for attr in dir(self):
            if attr.endswith('_window') or attr.endswith('_view'):
                win = getattr(self, attr)
                if hasattr(win, 'close'):
                    try:
                        win.close()
                    except Exception:
                        pass
        self.close()
        QApplication.quit()

    def apply_theme(self, theme_name):
        """
        تطبيق سمة (Theme) على البرنامج حسب الاسم.
        """
        app = QtWidgets.QApplication.instance()
        if theme_name == 'افتراضي':
            try:
                with open('resources/styles/app_style.qss', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
            except Exception:
                app.setStyleSheet("")
        elif theme_name == 'داكن':
            try:
                import qdarkstyle
                app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt5())
            except Exception:
                pass
        # يمكن إضافة سمات أخرى هنا
        # مثال: إخفاء زر التقارير إذا لم يملك المستخدم صلاحية 'view_reports'
        for btn in getattr(self, 'sidebar_btns', []):
            if btn.toolTip() == 'التقارير' and 'view_reports' not in self.effective_permissions:
                btn.setVisible(False)
            if btn.toolTip() == 'الإحصائيات' and 'view_statistics' not in self.effective_permissions:
                btn.setVisible(False)
            if btn.toolTip() == 'تحليل المنتجات' and 'product_analysis' not in self.effective_permissions:
                btn.setVisible(False)

    def show_user_guide(self):
        """
        عرض نافذة تحتوي على دليل المستخدم من ملف resources/help/USER_GUIDE.txt
        """
        dlg = QtWidgets.QDialog(self)
        dlg.setWindowTitle('دليل المستخدم')
        dlg.setMinimumSize(700, 600)
        layout = QtWidgets.QVBoxLayout(dlg)
        text_edit = QtWidgets.QTextEdit()
        text_edit.setReadOnly(True)
        font = QtGui.QFont('Cairo', 12)
        text_edit.setFont(font)
        # جعل النص أسود والخلفية بيضاء
        text_edit.setStyleSheet('color: #111; background: #fff;')
        try:
            with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'resources', 'help', 'USER_GUIDE.txt'), encoding='utf-8') as f:
                text_edit.setPlainText(f.read())
        except Exception as e:
            text_edit.setPlainText(f'تعذر تحميل دليل المستخدم: {e}')
        layout.addWidget(text_edit)
        btn_close = QtWidgets.QPushButton('إغلاق')
        btn_close.clicked.connect(dlg.accept)
        layout.addWidget(btn_close)
        dlg.exec_()
