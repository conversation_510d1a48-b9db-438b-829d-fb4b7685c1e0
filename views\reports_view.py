# نافذة التقارير والتحليلات
# تعرض تقارير المبيعات والأرباح مع تصدير PDF/Excel
# تاريخ آخر تعديل: 2025-05-03

from PyQt5 import QtWidgets, QtCore, QtGui
import sqlite3
from models import database
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import pandas as pd
import matplotlib
import arabic_reshaper
from bidi.algorithm import get_display
import matplotlib
from utils.utilities import parse_permissions, get_cogs_fifo, get_profit_fifo
from utils.threading_utils import WorkerThread
from main import get_app_icon
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['font.family'] = ['Cairo', 'Amiri', 'Arial', 'Tahoma', 'sans-serif']

def ar_text(text):
    reshaped = arabic_reshaper.reshape(text)
    return get_display(reshaped)

class ReportsWindow(QtWidgets.QWidget):
    def __init__(self, username, role, permissions=None):
        super().__init__()
        self.username = username  # اسم المستخدم الحالي
        self.role = role          # دور المستخدم
        self.permissions = parse_permissions(permissions)
        if self.role != 'manager' and 'view_reports' not in self.permissions:
            QtWidgets.QMessageBox.critical(self, 'صلاحيات غير كافية', 'ليس لديك صلاحية الوصول لهذه الشاشة.')
            self.close()
            return
        self.setWindowTitle('التقارير والتحليلات')
        self.setGeometry(400, 150, 700, 500)
        # تعيين أيقونة النافذة
        self.setWindowIcon(get_app_icon())
        self.setup_ui()
        self.load_report()

    def setup_ui(self):
        """
        إعداد واجهة المستخدم لنافذة التقارير.
        """
        layout = QtWidgets.QVBoxLayout()
        filter_layout = QtWidgets.QHBoxLayout()
        self.period_combo = QtWidgets.QComboBox()
        self.period_combo.addItems(['يومي', 'شهري', 'سنوي'])
        self.period_combo.currentIndexChanged.connect(self.load_report)
        filter_layout.addWidget(QtWidgets.QLabel('نوع التقرير:'))
        filter_layout.addWidget(self.period_combo)
        layout.addLayout(filter_layout)

        self.table = QtWidgets.QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(['الفترة', 'إجمالي المبيعات', 'صافي الربح'])
        self.table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.table)

        self.figure = plt.Figure(figsize=(5,2))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        self.setLayout(layout)
        # دعم RTL والخط العربي
        self.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.table.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.table.horizontalHeader().setLayoutDirection(QtCore.Qt.RightToLeft)
        self.table.setStyleSheet('font-family: "Cairo", "Tajawal", "Amiri", "Arial", sans-serif; font-size: 13pt;')

        # أزرار التصدير
        export_layout = QtWidgets.QHBoxLayout()
        self.export_pdf_btn = QtWidgets.QPushButton('تصدير إلى PDF')
        self.export_pdf_btn.clicked.connect(self.export_pdf)
        self.export_excel_btn = QtWidgets.QPushButton('تصدير إلى Excel')
        self.export_excel_btn.clicked.connect(self.export_excel)
        export_layout.addWidget(self.export_pdf_btn)
        export_layout.addWidget(self.export_excel_btn)
        layout.addLayout(export_layout)
        # تعطيل أزرار التصدير إذا لم تتوفر صلاحية view_reports
        if 'view_reports' not in self.permissions and self.role != 'manager':
            self.export_pdf_btn.setEnabled(False)
            self.export_excel_btn.setEnabled(False)
        # زر تقرير المرتجعات
        returns_btn = QtWidgets.QPushButton('تقرير المرتجعات')
        returns_btn.setStyleSheet('background-color: #ffff00; color: black; font-size: 16pt; border-radius: 18px; font-weight: bold; font-family: Cairo, Arial, sans-serif;')
        returns_btn.clicked.connect(self.show_returns_report)
        layout.addWidget(returns_btn)

        # زر تحليل المنتجات
        products_analysis_btn = QtWidgets.QPushButton('تحليل المنتجات الأكثر والأقل مبيعًا')
        products_analysis_btn.setStyleSheet('background-color: #b2dfdb; color: #263238; font-size: 14pt; font-weight: bold; font-family: Cairo, Arial, sans-serif;')
        products_analysis_btn.clicked.connect(self.show_products_analysis)
        layout.addWidget(products_analysis_btn)

        # زر طباعة التقرير
        print_btn = QtWidgets.QPushButton('طباعة التقرير')
        print_btn.clicked.connect(self.print_current_report)
        layout.addWidget(print_btn)

    def export_pdf(self):
        """
        تصدير التقرير الحالي إلى ملف PDF.
        """
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        from reportlab.lib import colors
        from reportlab.platypus import Table, TableStyle
        file_path, _ = QtWidgets.QFileDialog.getSaveFileName(self, 'حفظ التقرير كـ PDF', 'report.pdf', 'PDF Files (*.pdf)')
        if not file_path:
            return
        data = [[self.table.horizontalHeaderItem(i).text() for i in range(self.table.columnCount())]]
        for row in range(self.table.rowCount()):
            data.append([self.table.item(row, col).text() if self.table.item(row, col) else '' for col in range(self.table.columnCount())])
        c = canvas.Canvas(file_path, pagesize=A4)
        width, height = A4
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0,0), (-1,0), colors.lightblue),
            ('TEXTCOLOR', (0,0), (-1,0), colors.whitesmoke),
            ('ALIGN', (0,0), (-1,-1), 'CENTER'),
            ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0,0), (-1,0), 12),
            ('BACKGROUND', (0,1), (-1,-1), colors.beige),
            ('GRID', (0,0), (-1,-1), 1, colors.black),
        ]))
        table.wrapOn(c, width, height)
        table.drawOn(c, 30, height - 40 * (len(data)+2))
        c.save()
        QtWidgets.QMessageBox.information(self, 'تم', 'تم تصدير التقرير إلى PDF بنجاح!')

    def export_excel(self):
        """
        تصدير التقرير الحالي إلى ملف Excel.
        """
        import pandas as pd
        file_path, _ = QtWidgets.QFileDialog.getSaveFileName(self, 'حفظ التقرير كـ Excel', 'report.xlsx', 'Excel Files (*.xlsx)')
        if not file_path:
            return
        data = []
        headers = [self.table.horizontalHeaderItem(i).text() for i in range(self.table.columnCount())]
        for row in range(self.table.rowCount()):
            data.append([self.table.item(row, col).text() if self.table.item(row, col) else '' for col in range(self.table.columnCount())])
        df = pd.DataFrame(data, columns=headers)
        df.to_excel(file_path, index=False)
        QtWidgets.QMessageBox.information(self, 'تم', 'تم تصدير التقرير إلى Excel بنجاح!')

    def load_report(self):
        """
        تحميل بيانات التقرير حسب الفترة المختارة (يومي/شهري/سنوي) وعرضها في الجدول والرسم البياني.
        الآن تعمل في Thread منفصل لتسريع الأداء ومنع التجميد.
        """
        self.table.setRowCount(0)
        self.figure.clear()
        self.canvas.draw()
        self._report_thread = WorkerThread(self._load_report_data, self.period_combo.currentText())
        self._report_thread.result_ready.connect(self._on_report_ready)
        self._report_thread.error.connect(self._on_report_error)
        self._report_thread.start()

    def _load_report_data(self, period):
        import arabic_reshaper
        from bidi.algorithm import get_display
        import sqlite3
        import pandas as pd
        from utils.utilities import get_profit_fifo
        conn = sqlite3.connect(database.DB_NAME)
        if period == 'يومي':
            sales_query = '''SELECT sale_date as period, si.product_id, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales FROM sales_items si JOIN sales s ON si.sale_id=s.id GROUP BY sale_date, si.product_id ORDER BY sale_date DESC LIMIT 300'''
        elif period == 'شهري':
            sales_query = '''SELECT substr(s.sale_date,1,7) as period, si.product_id, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales FROM sales_items si JOIN sales s ON si.sale_id=s.id GROUP BY substr(s.sale_date,1,7), si.product_id ORDER BY period DESC LIMIT 120'''
        else:
            sales_query = '''SELECT substr(s.sale_date,1,4) as period, si.product_id, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales FROM sales_items si JOIN sales s ON si.sale_id=s.id GROUP BY substr(s.sale_date,1,4), si.product_id ORDER BY period DESC LIMIT 50'''
        df_sales = pd.read_sql_query(sales_query, conn)
        conn.close()
        df_grouped = df_sales.groupby('period').agg({'sales': 'sum'}).reset_index()
        df_grouped['profit'] = df_grouped['period'].apply(lambda p: get_profit_fifo(df_sales[df_sales['period'] == p]))
        df_grouped['period'] = df_grouped['period'].apply(lambda x: get_display(arabic_reshaper.reshape(str(x))))
        chart_data = {
            'labels': df_grouped['period'].tolist(),
            'sales': df_grouped['sales'].tolist(),
            'profits': df_grouped['profit'].tolist(),
        }
        return {'df_grouped': df_grouped, 'chart_data': chart_data}

    def _on_report_ready(self, result):
        df_grouped = result['df_grouped']
        chart_data = result['chart_data']
        self.table.setRowCount(len(df_grouped))
        for i, row in df_grouped.iterrows():
            self.table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(row['period'])))
            self.table.setItem(i, 1, QtWidgets.QTableWidgetItem(f"{row['sales']:.2f}"))
            self.table.setItem(i, 2, QtWidgets.QTableWidgetItem(f"{row['profit']:.2f}"))
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        labels = chart_data['labels']
        sales = chart_data['sales']
        profits = chart_data['profits']
        bars = ax.bar(labels, sales, color='#4F8A8B', label=ar_text('المبيعات'))
        ax.bar(labels, profits, color='#43a047', label=ar_text('الأرباح'), alpha=0.7)
        for bar in bars:
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height(), f'{bar.get_height()}', ha='center', va='bottom', fontsize=12, fontweight='bold')
        ax.set_title(ar_text('تقرير المبيعات والأرباح'), fontsize=16, fontweight='bold')
        ax.set_ylabel(ar_text('القيمة'), fontsize=14)
        ax.set_xlabel(ar_text('الفترة'), fontsize=14)
        ax.legend(loc='upper right', fontsize=12)
        ax.invert_xaxis()
        self.figure.tight_layout()
        self.canvas.draw()

    def _on_report_error(self, error):
        QtWidgets.QMessageBox.critical(self, 'خطأ', f'حدث خطأ أثناء تحميل التقرير:\n{error}')

    def show_returns_report(self):
        """
        عرض تقرير مرتجعات المبيعات مع إمكانية التصدير.
        """
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle('تقرير مرتجعات المبيعات')
        dialog.resize(700, 500)
        vbox = QtWidgets.QVBoxLayout(dialog)
        table = QtWidgets.QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(['رقم الفاتورة', 'اسم المنتج', 'الكمية المرتجعة', 'تاريخ الإرجاع', 'السبب'])
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('''SELECT sr.sale_id, p.name, sr.quantity, sr.return_date, sr.reason FROM sales_returns sr JOIN products p ON sr.product_id = p.id ORDER BY sr.return_date DESC''')
        rows = c.fetchall()
        conn.close()
        table.setRowCount(len(rows))
        for i, row in enumerate(rows):
            for j, val in enumerate(row):
                table.setItem(i, j, QtWidgets.QTableWidgetItem(str(val)))
        vbox.addWidget(table)
        # أزرار التصدير
        btns_layout = QtWidgets.QHBoxLayout()
        export_pdf_btn = QtWidgets.QPushButton('تصدير إلى PDF')
        export_excel_btn = QtWidgets.QPushButton('تصدير إلى Excel')
        btns_layout.addWidget(export_pdf_btn)
        btns_layout.addWidget(export_excel_btn)
        close_btn = QtWidgets.QPushButton('إغلاق')
        close_btn.setStyleSheet('background-color: #e53935; color: white; font-size: 16pt; border-radius: 18px; font-weight: bold; font-family: Cairo, Arial, sans-serif;')
        btns_layout.addWidget(close_btn)
        vbox.addLayout(btns_layout)
        close_btn.clicked.connect(dialog.accept)
        # تصدير PDF
        def export_pdf():
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            from reportlab.lib import colors
            from reportlab.platypus import Table, TableStyle
            file_path, _ = QtWidgets.QFileDialog.getSaveFileName(dialog, 'حفظ تقرير المرتجعات كـ PDF', 'returns_report.pdf', 'PDF Files (*.pdf)')
            if not file_path:
                return
            data = [[table.horizontalHeaderItem(i).text() for i in range(table.columnCount())]]
            for row in range(table.rowCount()):
                data.append([table.item(row, col).text() if table.item(row, col) else '' for col in range(table.columnCount())])
            c = canvas.Canvas(file_path, pagesize=A4)
            width, height = A4
            t = Table(data)
            t.setStyle(TableStyle([
                ('BACKGROUND', (0,0), (-1,0), colors.lightblue),
                ('TEXTCOLOR', (0,0), (-1,0), colors.whitesmoke),
                ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
                ('BOTTOMPADDING', (0,0), (-1,0), 12),
                ('BACKGROUND', (0,1), (-1,-1), colors.beige),
                ('GRID', (0,0), (-1,-1), 1, colors.black),
            ]))
            t.wrapOn(c, width, height)
            t.drawOn(c, 30, height - 40 * (len(data)+2))
            c.save()
            QtWidgets.QMessageBox.information(dialog, 'تم', 'تم تصدير تقرير المرتجعات إلى PDF بنجاح!')
        export_pdf_btn.clicked.connect(export_pdf)
        # تصدير Excel
        def export_excel():
            import pandas as pd
            file_path, _ = QtWidgets.QFileDialog.getSaveFileName(dialog, 'حفظ تقرير المرتجعات كـ Excel', 'returns_report.xlsx', 'Excel Files (*.xlsx)')
            if not file_path:
                return
            data = []
            headers = [table.horizontalHeaderItem(i).text() for i in range(table.columnCount())]
            for row in range(table.rowCount()):
                data.append([table.item(row, col).text() if table.item(row, col) else '' for col in range(table.columnCount())])
            df = pd.DataFrame(data, columns=headers)
            df.to_excel(file_path, index=False)
            QtWidgets.QMessageBox.information(dialog, 'تم', 'تم تصدير تقرير المرتجعات إلى Excel بنجاح!')
        export_excel_btn.clicked.connect(export_excel)
        dialog.exec_()

    def show_products_analysis(self):
        """
        تحسين نافذة تحليل المنتجات لتعرض المنتجات الأكثر والأقل مبيعًا، مع حساب الربح بدقة باستخدام تكلفة الشراء الفعلية FIFO.
        """
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle('تحليل المنتجات الأكثر والأقل مبيعًا')
        dialog.resize(900, 600)
        vbox = QtWidgets.QVBoxLayout(dialog)
        # اختيار الفترة
        period_combo = QtWidgets.QComboBox()
        period_combo.addItems(['يومي', 'شهري', 'سنوي'])
        vbox.addWidget(QtWidgets.QLabel('الفترة:'))
        vbox.addWidget(period_combo)
        tabs = QtWidgets.QTabWidget()
        table_most = QtWidgets.QTableWidget()
        table_most.setColumnCount(4)
        table_most.setHorizontalHeaderLabels(['اسم المنتج', 'الكمية المباعة', 'إجمالي المبيعات', 'صافي الربح'])
        table_most.horizontalHeader().setStretchLastSection(True)
        tabs.addTab(table_most, 'الأكثر مبيعًا')
        table_least = QtWidgets.QTableWidget()
        table_least.setColumnCount(4)
        table_least.setHorizontalHeaderLabels(['اسم المنتج', 'الكمية المباعة', 'إجمالي المبيعات', 'صافي الربح'])
        table_least.horizontalHeader().setStretchLastSection(True)
        tabs.addTab(table_least, 'الأقل مبيعًا')
        vbox.addWidget(tabs)
        def load_products_stats():
            period = period_combo.currentText()
            conn = sqlite3.connect(database.DB_NAME)
            if period == 'يومي':
                date_limit = "AND sale_date >= date('now', '-30 day')"
            elif period == 'شهري':
                date_limit = "AND sale_date >= date('now', '-12 month')"
            else:
                date_limit = ''
            # جلب الكمية والمبيعات لكل منتج
            query = f'''
                SELECT p.name, SUM(si.quantity) as qty, SUM(si.quantity*si.sale_price) as sales, p.id
                FROM sales_items si
                JOIN sales s ON si.sale_id = s.id
                JOIN products p ON si.product_id = p.id
                WHERE 1=1 {date_limit}
                GROUP BY p.id
                ORDER BY sales DESC
            '''
            df_sales = pd.read_sql_query(query, conn)
            # جلب تكلفة الشراء الفعلية FIFO لكل منتج
            from utils.utilities import get_cogs_fifo
            df_sales['profit'] = 0.0
            for idx, row in df_sales.iterrows():
                product_id = row['id']
                qty = int(row['qty']) if not pd.isnull(row['qty']) else 0
                cogs = get_cogs_fifo(product_id, qty)
                df_sales.at[idx, 'profit'] = row['sales'] - cogs
            # الأكثر مبيعًا
            df_most = df_sales.sort_values('sales', ascending=False).head(30)
            table_most.setRowCount(len(df_most))
            for i, row in df_most.iterrows():
                table_most.setItem(i, 0, QtWidgets.QTableWidgetItem(str(row['name'])))
                table_most.setItem(i, 1, QtWidgets.QTableWidgetItem(str(int(row['qty']))))
                table_most.setItem(i, 2, QtWidgets.QTableWidgetItem(f"{row['sales']:.2f}"))
                table_most.setItem(i, 3, QtWidgets.QTableWidgetItem(f"{row['profit']:.2f}"))
            # الأقل مبيعًا
            df_least = df_sales.sort_values('sales', ascending=True).head(30)
            table_least.setRowCount(len(df_least))
            for i, row in df_least.iterrows():
                table_least.setItem(i, 0, QtWidgets.QTableWidgetItem(str(row['name'])))
                table_least.setItem(i, 1, QtWidgets.QTableWidgetItem(str(int(row['qty']))))
                table_least.setItem(i, 2, QtWidgets.QTableWidgetItem(f"{row['sales']:.2f}"))
                table_least.setItem(i, 3, QtWidgets.QTableWidgetItem(f"{row['profit']:.2f}"))
            conn.close()
        period_combo.currentIndexChanged.connect(load_products_stats)
        load_products_stats()
        # زر تصدير
        export_layout = QtWidgets.QHBoxLayout()
        export_pdf_btn = QtWidgets.QPushButton('تصدير إلى PDF')
        export_excel_btn = QtWidgets.QPushButton('تصدير إلى Excel')
        export_layout.addWidget(export_pdf_btn)
        export_layout.addWidget(export_excel_btn)
        vbox.addLayout(export_layout)
        def export_pdf():
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            from reportlab.lib import colors
            from reportlab.platypus import Table, TableStyle
            file_path, _ = QtWidgets.QFileDialog.getSaveFileName(dialog, 'حفظ تحليل المنتجات كـ PDF', 'products_analysis.pdf', 'PDF Files (*.pdf)')
            if not file_path:
                return
            # تصدير الأكثر مبيعًا فقط
            data = [[table_most.horizontalHeaderItem(i).text() for i in range(table_most.columnCount())]]
            for row in range(table_most.rowCount()):
                data.append([table_most.item(row, col).text() if table_most.item(row, col) else '' for col in range(table_most.columnCount())])
            c = canvas.Canvas(file_path, pagesize=A4)
            width, height = A4
            t = Table(data)
            t.setStyle(TableStyle([
                ('BACKGROUND', (0,0), (-1,0), colors.lightblue),
                ('TEXTCOLOR', (0,0), (-1,0), colors.whitesmoke),
                ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
                ('BOTTOMPADDING', (0,0), (-1,0), 12),
                ('BACKGROUND', (0,1), (-1,-1), colors.beige),
                ('GRID', (0,0), (-1,-1), 1, colors.black),
            ]))
            t.wrapOn(c, width, height)
            t.drawOn(c, 30, height - 40 * (len(data)+2))
            c.save()
            QtWidgets.QMessageBox.information(dialog, 'تم', 'تم تصدير تحليل المنتجات إلى PDF بنجاح!')
        export_pdf_btn.clicked.connect(export_pdf)
        def export_excel():
            import pandas as pd
            file_path, _ = QtWidgets.QFileDialog.getSaveFileName(dialog, 'حفظ تحليل المنتجات كـ Excel', 'products_analysis.xlsx', 'Excel Files (*.xlsx)')
            if not file_path:
                return
            data = []
            headers = [table_most.horizontalHeaderItem(i).text() for i in range(table_most.columnCount())]
            for row in range(table_most.rowCount()):
                data.append([table_most.item(row, col).text() if table_most.item(row, col) else '' for col in range(table_most.columnCount())])
            df = pd.DataFrame(data, columns=headers)
            df.to_excel(file_path, index=False)
            QtWidgets.QMessageBox.information(dialog, 'تم', 'تم تصدير تحليل المنتجات إلى Excel بنجاح!')
        export_excel_btn.clicked.connect(export_excel)
        # زر إغلاق
        close_btn = QtWidgets.QPushButton('إغلاق')
        close_btn.clicked.connect(dialog.accept)
        vbox.addWidget(close_btn)
        dialog.exec_()

    def print_current_report(self):
        """
        توليد HTML للتقرير الحالي وطباعته باستخدام print_invoice.
        """
        from utils.print_utils import print_invoice
        html = self.generate_report_html()  # دالة تولد HTML للتقرير
        print_invoice(html, self)

    def generate_report_html(self):
        """
        مثال مبسط لتوليد HTML للتقرير. عدل القالب حسب احتياجك.
        """
        # مثال: توليد جدول من بيانات التقرير
        items_html = ""
        for row in self.get_report_rows():  # يجب أن تعيد هذه الدالة بيانات التقرير
            items_html += f"<tr>{''.join(f'<td>{cell}</td>' for cell in row)}</tr>"
        html = f"""
        <html>
        <head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}}</style></head>
        <body dir='rtl'>
        <h2 style='text-align:center'>تقرير</h2>
        <table>{items_html}</table>
        </body></html>
        """
        return html

    def get_report_rows(self):
        """
        تعيد بيانات التقرير الحالي كقائمة صفوف (لكل صف قائمة أعمدة)، مأخوذة من جدول QTableWidget.
        """
        rows = []
        for row in range(self.table.rowCount()):
            row_data = []
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                row_data.append(item.text() if item else '')
            rows.append(row_data)
        return rows
