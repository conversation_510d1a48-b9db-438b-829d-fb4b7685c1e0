# event_bus.py
# نظام إشعارات مركزي لتحديث جميع النوافذ تلقائياً
from PyQt5.QtCore import QObject, pyqtSignal

class EventBus(QObject):
    data_changed = pyqtSignal(str)  # str: نوع البيانات ("product", "purchase", ...)
    product_added = pyqtSignal()
    product_updated = pyqtSignal()
    product_deleted = pyqtSignal()
    purchase_made = pyqtSignal()
    sale_made = pyqtSignal()
    inventory_changed = pyqtSignal()
    customer_changed = pyqtSignal()
    supplier_changed = pyqtSignal()
    # أضف إشارات أخرى حسب الحاجة

# كائن وحيد (Singleton)
event_bus = EventBus()
