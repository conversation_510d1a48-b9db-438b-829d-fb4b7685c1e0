# نافذة الإعدادات
# لإدارة المستخدمين والنسخ الاحتياطي
# تاريخ آخر تعديل: 2025-05-03

from PyQt5 import QtWidgets, QtCore, QtGui
import sqlite3
from models import database
import shutil
import os
from utils.messages import get_message

class SettingsWindow(QtWidgets.QWidget):
    def __init__(self, username, role, permissions=None):
        super().__init__()
        from utils.print_utils import get_icon
        self.setWindowIcon(QtGui.QIcon(get_icon('smart.ico')))
        self.username = username  # اسم المستخدم الحالي
        self.role = role          # دور المستخدم
        self.user_permissions = permissions or []  # قائمة الصلاحيات للمستخدم الحالي
        # تعيين الثيم الداكن تلقائياً عند فتح نافذة الإعدادات
        try:
            import qdarkstyle
            app = QtWidgets.QApplication.instance()
            app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt5())
        except Exception:
            pass
        if self.role != 'manager':
            QtWidgets.QMessageBox.critical(self, 'صلاحيات غير كافية', get_message('error_permission'))
            self.close()
            return
        self.setWindowTitle('الإعدادات')
        self.setGeometry(500, 200, 500, 400)
        self.setup_ui()
        self.load_users()

    def setup_ui(self):
        """
        إعداد واجهة المستخدم لنافذة الإعدادات (إدارة المستخدمين، النسخ الاحتياطي، المظهر).
        """
        main_layout = QtWidgets.QVBoxLayout(self)

        # --- إعدادات المظهر ---
        appearance_group = QtWidgets.QGroupBox('إعدادات المظهر')
        appearance_layout = QtWidgets.QFormLayout()
        self.theme_combo = QtWidgets.QComboBox()
        self.theme_combo.addItems(['افتراضي', 'داكن', 'أزرق', 'أخضر', 'أحمر', 'رملي'])
        self.theme_combo.currentTextChanged.connect(self.apply_theme)
        appearance_layout.addRow('السمة:', self.theme_combo)
        from PyQt5.QtGui import QFont
        self.font_combo = QtWidgets.QFontComboBox()
        self.font_combo.setFontFilters(QtWidgets.QFontComboBox.AllFonts)
        self.font_combo.setCurrentFont(QtGui.QFont('Cairo'))
        self.font_combo.currentFontChanged.connect(self.apply_font)
        appearance_layout.addRow('الخط:', self.font_combo)
        # إعدادات الاتجاه (RTL/LTR)
        self.direction_combo = QtWidgets.QComboBox()
        self.direction_combo.addItems(['من اليمين لليسار', 'من اليسار لليمين'])
        self.direction_combo.currentIndexChanged.connect(self.apply_direction)
        appearance_layout.addRow('اتجاه النص:', self.direction_combo)
        appearance_group.setLayout(appearance_layout)
        main_layout.addWidget(appearance_group)

        # --- إدارة المستخدمين ---
        user_group = QtWidgets.QGroupBox('إدارة المستخدمين')
        user_layout = QtWidgets.QVBoxLayout()
        self.users_table = QtWidgets.QTableWidget()
        self.users_table.setColumnCount(3)
        self.users_table.setHorizontalHeaderLabels(['اسم المستخدم', 'الدور', ''])
        self.users_table.horizontalHeader().setStretchLastSection(True)
        user_layout.addWidget(self.users_table)
        add_user_btn = QtWidgets.QPushButton('إضافة مستخدم جديد')
        add_user_btn.setStyleSheet('background-color: #ff00ff; color: white; font-weight: bold; border-radius: 10px; padding: 10px 18px;')
        add_user_btn.clicked.connect(self.add_user_dialog)
        # تعطيل زر إضافة مستخدم إذا لم تكن الصلاحية موجودة
        if 'edit' not in self.user_permissions and self.role != 'manager':
            add_user_btn.setEnabled(False)
            add_user_btn.setStyleSheet(add_user_btn.styleSheet() + 'opacity:0.5;')
        user_layout.addWidget(add_user_btn)
        user_group.setLayout(user_layout)
        main_layout.addWidget(user_group)

        # --- النسخ الاحتياطي ---
        backup_group = QtWidgets.QGroupBox('النسخ الاحتياطي')
        backup_layout = QtWidgets.QVBoxLayout()
        backup_btn = QtWidgets.QPushButton('عمل نسخة احتياطية من قاعدة البيانات')
        backup_btn.clicked.connect(self.backup_db)
        # تعطيل زر النسخ الاحتياطي إذا لم تكن الصلاحية موجودة
        if 'backup_db' not in self.user_permissions and self.role != 'manager':
            backup_btn.setEnabled(False)
            backup_btn.setStyleSheet('background-color: #aaa; color: #fff; opacity:0.5;')
        backup_layout.addWidget(backup_btn)
        self.status_label = QtWidgets.QLabel('')
        backup_layout.addWidget(self.status_label)
        backup_group.setLayout(backup_layout)
        main_layout.addWidget(backup_group)

        self.setLayout(main_layout)

    def load_users(self):
        """
        تحميل وعرض جميع المستخدمين في الجدول.
        """
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT username, role FROM users')
        users = c.fetchall()
        conn.close()
        self.users_table.setRowCount(len(users))
        for i, (username, role) in enumerate(users):
            self.users_table.setItem(i, 0, QtWidgets.QTableWidgetItem(username))
            self.users_table.setItem(i, 1, QtWidgets.QTableWidgetItem(role))
            del_btn = QtWidgets.QPushButton('حذف')
            del_btn.setStyleSheet('background-color: #e53935; color: white; font-weight: bold; border-radius: 10px; padding: 10px 18px;')
            del_btn.clicked.connect(lambda _, u=username: self.delete_user(u))
            # تعطيل زر الحذف إذا لم تكن الصلاحية موجودة أو إذا كان المستخدم admin
            if username == 'admin':
                del_btn.setEnabled(False)
                del_btn.setToolTip('لا يمكن حذف المستخدم admin')
                del_btn.setStyleSheet(del_btn.styleSheet() + 'opacity:0.5;')
            elif 'delete' not in self.user_permissions and self.role != 'manager':
                del_btn.setEnabled(False)
                del_btn.setStyleSheet(del_btn.styleSheet() + 'opacity:0.5;')
            self.users_table.setCellWidget(i, 2, del_btn)

        # تعطيل الجدول بالكامل إذا لم تكن الصلاحية موجودة
        if 'edit' not in self.user_permissions and self.role != 'manager':
            self.users_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
            self.users_table.setStyleSheet('opacity:0.7;')

    def add_user_dialog(self):
        """
        نافذة إضافة مستخدم جديد مع التحقق من صحة البيانات وتحديد الصلاحيات.
        """
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle('إضافة مستخدم جديد')
        dialog.setMinimumWidth(480)
        dialog.setMinimumHeight(600)
        dialog.setStyleSheet('QDialog { background: #f7f7fa; }')

        main_layout = QtWidgets.QVBoxLayout(dialog)
        main_layout.setContentsMargins(18, 18, 18, 18)
        main_layout.setSpacing(12)

        form_widget = QtWidgets.QWidget()
        form = QtWidgets.QFormLayout(form_widget)
        form.setLabelAlignment(QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
        form.setFormAlignment(QtCore.Qt.AlignTop)
        form.setHorizontalSpacing(18)
        form.setVerticalSpacing(16)

        username = QtWidgets.QLineEdit()
        username.setMinimumWidth(220)
        username.setStyleSheet('padding: 7px; font-size: 15px;')
        password = QtWidgets.QLineEdit()
        password.setEchoMode(QtWidgets.QLineEdit.Password)
        password.setMinimumWidth(220)
        password.setStyleSheet('padding: 7px; font-size: 15px;')
        role = QtWidgets.QComboBox()
        role.addItems(['manager', 'cashier', 'purchaser'])
        role.setStyleSheet('padding: 7px; font-size: 15px;')
        form.addRow('اسم المستخدم:', username)
        form.addRow('كلمة المرور:', password)
        form.addRow('الدور:', role)

        # --- الصلاحيات ---
        permissions_group = QtWidgets.QGroupBox('الصلاحيات الإضافية')
        permissions_group.setStyleSheet('QGroupBox { font-weight: bold; color: #333; border: 1px solid #bbb; border-radius: 8px; margin-top: 10px; padding-top: 8px; background: #fff; } QGroupBox:title { subcontrol-origin: margin; left: 10px; padding: 0 3px 0 3px; }')
        perm_layout = QtWidgets.QVBoxLayout()
        perm_layout.setSpacing(7)
        perm_layout.setContentsMargins(8, 8, 8, 8)

        # شريط البحث عن صلاحية
        search_layout = QtWidgets.QHBoxLayout()
        search_label = QtWidgets.QLabel('بحث عن صلاحية:')
        search_edit = QtWidgets.QLineEdit()
        search_edit.setPlaceholderText('اكتب جزء من اسم أو وصف الصلاحية...')
        search_edit.setStyleSheet('padding: 5px; font-size: 14px;')
        search_layout.addWidget(search_label)
        search_layout.addWidget(search_edit)
        perm_layout.addLayout(search_layout)

        # أزرار تحديد الكل/إلغاء الكل
        btns_layout = QtWidgets.QHBoxLayout()
        select_all_btn = QtWidgets.QPushButton('تحديد الكل')
        deselect_all_btn = QtWidgets.QPushButton('إلغاء الكل')
        select_all_btn.setStyleSheet('padding: 5px 15px; background: #1976d2; color: #fff; border-radius: 6px; font-weight: bold;')
        deselect_all_btn.setStyleSheet('padding: 5px 15px; background: #aaa; color: #fff; border-radius: 6px; font-weight: bold;')
        btns_layout.addWidget(select_all_btn)
        btns_layout.addWidget(deselect_all_btn)
        perm_layout.addLayout(btns_layout)

        # تقسيم الصلاحيات إلى مجموعات
        perm_groups = [
            ('صلاحيات الإدارة', [
                ('delete', 'يمكنه الحذف', 'السماح بحذف السجلات (منتجات، عملاء، موردين، ...).'),
                ('edit', 'يمكنه التعديل', 'السماح بتعديل السجلات والبيانات.'),
            ]),
            ('صلاحيات التقارير والتحليل', [
                ('view_reports', 'يمكنه رؤية التقارير', 'عرض تقارير المبيعات والمشتريات والعملاء والموردين.'),
                ('view_statistics', 'يمكنه رؤية الإحصائيات', 'عرض إحصائيات وتحليلات الأداء.'),
                ('product_analysis', 'يمكنه تحليل المنتجات', 'الوصول إلى أدوات تحليل المنتجات.'),
            ]),
            ('صلاحيات العمليات اليومية', [
                ('manage_products', 'يمكنه إدارة المنتجات', 'إضافة وتعديل المنتجات وإدارة المخزون.'),
                ('manage_suppliers', 'يمكنه إدارة الموردين', 'إضافة وتعديل وحذف الموردين.'),
                ('manage_customers', 'يمكنه إدارة العملاء', 'إضافة وتعديل وحذف العملاء.'),
            ]),
        ]
        perm_checkboxes = {}
        perm_widgets = []  # (group_label, cb, perm, label, tooltip)
        for group_name, perms in perm_groups:
            group_label = QtWidgets.QLabel(group_name)
            group_label.setStyleSheet('font-weight: bold; color: #1976d2; margin-top: 8px; margin-bottom: 2px;')
            perm_layout.addWidget(group_label)
            for perm, label, tooltip in perms:
                cb = QtWidgets.QCheckBox(label)
                cb.setToolTip(tooltip)
                cb.setStyleSheet('padding: 3px 0 3px 0; font-size: 14px;')
                perm_layout.addWidget(cb)
                perm_checkboxes[perm] = cb
                perm_widgets.append((group_label, cb, perm, label, tooltip))

        # دالة تصفية الصلاحيات حسب البحث
        def filter_permissions():
            text = search_edit.text().strip().lower()
            for group_label, cb, perm, label, tooltip in perm_widgets:
                match = (
                    text in perm.lower() or
                    text in label.lower() or
                    text in tooltip.lower()
                )
                cb.setVisible(match or text == '')
            # إظهار/إخفاء عناوين المجموعات حسب العناصر
            group_map = {}
            for group_label, cb, *_ in perm_widgets:
                if group_label not in group_map:
                    group_map[group_label] = []
                group_map[group_label].append(cb)
            for group_label, cbs in group_map.items():
                visible = any(cb.isVisible() for cb in cbs)
                group_label.setVisible(visible)
        search_edit.textChanged.connect(filter_permissions)
        filter_permissions()

        # إضافة ScrollArea للصلاحيات
        scroll = QtWidgets.QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QtWidgets.QWidget()
        scroll_content.setLayout(perm_layout)
        scroll.setWidget(scroll_content)
        scroll.setMinimumHeight(220)
        scroll.setMaximumHeight(320)
        permissions_group_layout = QtWidgets.QVBoxLayout(permissions_group)
        permissions_group_layout.setContentsMargins(2, 2, 2, 2)
        permissions_group_layout.addWidget(scroll)

        form.addRow(permissions_group)
        main_layout.addWidget(form_widget)
        main_layout.addWidget(permissions_group)

        # وظائف الأزرار
        def select_all():
            for cb in perm_checkboxes.values():
                cb.setChecked(True)
        def deselect_all():
            for cb in perm_checkboxes.values():
                cb.setChecked(False)
        select_all_btn.clicked.connect(select_all)
        deselect_all_btn.clicked.connect(deselect_all)
        # وظائف الأزرار
        def select_all():
            for cb in perm_checkboxes.values():
                cb.setChecked(True)
        def deselect_all():
            for cb in perm_checkboxes.values():
                cb.setChecked(False)
        select_all_btn.clicked.connect(select_all)
        deselect_all_btn.clicked.connect(deselect_all)

        # الصلاحيات الافتراضية حسب الدور
        def set_default_permissions(index):
            # manager: كل الصلاحيات
            # cashier: المبيعات، الشراء، التقارير، العملاء فقط (sales مفعلة دائماً)
            # purchaser: إدارة المشتريات وإدارة المنتجات من الأساسيات
            for perm, cb in perm_checkboxes.items():
                cb.setChecked(False)
            if index == 0:  # manager
                for cb in perm_checkboxes.values():
                    cb.setChecked(True)
            elif index == 1:  # cashier
                for perm in ['sales', 'purchases', 'view_reports', 'manage_customers']:
                    if perm in perm_checkboxes:
                        perm_checkboxes[perm].setChecked(True)
            elif index == 2:  # purchaser
                for perm in ['purchases', 'manage_products']:
                    if perm in perm_checkboxes:
                        perm_checkboxes[perm].setChecked(True)

        role.currentIndexChanged.connect(set_default_permissions)
        set_default_permissions(role.currentIndex())
        # --- نهاية الصلاحيات ---
        btns = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel)
        form.addRow(btns)
        btns.accepted.connect(dialog.accept)
        btns.rejected.connect(dialog.reject)
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            uname = username.text().strip()
            pwd = password.text().strip()
            r = role.currentText()
            # جمع الصلاحيات المختارة
            perms = [perm for perm, cb in perm_checkboxes.items() if cb.isChecked()]
            permissions_str = ','.join(perms)
            if not uname or not pwd:
                self.status_label.setText('يرجى إدخال اسم المستخدم وكلمة المرور')
                self.status_label.setStyleSheet('color: red')
                return
            import hashlib
            pwd_hash = hashlib.sha256(pwd.encode()).hexdigest()
            try:
                conn = sqlite3.connect(database.DB_NAME)
                c = conn.cursor()
                c.execute('INSERT INTO users (username, password, role, permissions) VALUES (?, ?, ?, ?)', (uname, pwd_hash, r, permissions_str))
                conn.commit()
                conn.close()
                self.status_label.setText(get_message('success_user_added'))
                self.status_label.setStyleSheet('color: green')
                self.load_users()
            except sqlite3.IntegrityError:
                self.status_label.setText(get_message('error_user_exists'))
                self.status_label.setStyleSheet('color: red')

    def delete_user(self, username):
        """
        حذف مستخدم من قاعدة البيانات (مع منع حذف admin الافتراضي).
        """
        if username == 'admin':
            self.status_label.setText(get_message('error_cannot_delete_admin'))
            self.status_label.setStyleSheet('color: red')
            return
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('DELETE FROM users WHERE username = ?', (username,))
        conn.commit()
        conn.close()
        self.status_label.setText(get_message('success_user_deleted'))
        self.status_label.setStyleSheet('color: green')
        self.load_users()

    def backup_db(self):
        """
        عمل نسخة احتياطية من قاعدة البيانات وحفظها في مكان يحدده المستخدم.
        """
        backup_path, _ = QtWidgets.QFileDialog.getSaveFileName(self, 'حفظ النسخة الاحتياطية', f'restaurant_backup_{QtCore.QDate.currentDate().toString("yyyyMMdd")}.db', 'Database Files (*.db)')
        if backup_path:
            try:
                shutil.copy(database.DB_NAME, backup_path)
                self.status_label.setText(get_message('success_backup'))
                self.status_label.setStyleSheet('color: green')
            except Exception as e:
                self.status_label.setText(f"{get_message('error_backup')}: {e}")
                self.status_label.setStyleSheet('color: red')

    def apply_theme(self, theme_name):
        app = QtWidgets.QApplication.instance()
        if theme_name == 'افتراضي':
            try:
                with open('resources/styles/app_style.qss', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
            except Exception:
                app.setStyleSheet("")
        elif theme_name == 'داكن':
            try:
                import qdarkstyle
                app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt5())
                # معالجة وضوح القوائم المنسدلة في الوضع الداكن
                app.setStyleSheet(app.styleSheet() + """
                    QComboBox, QComboBox QAbstractItemView {
                        background: #222;
                        color: #fff;
                        selection-background-color: #1976d2;
                        selection-color: #fff;
                    }
                """)
            except Exception:
                pass
        elif theme_name == 'أزرق':
            app.setStyleSheet("""
                QWidget { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e3f0ff, stop:1 #1976d2); color: #222; }
                QPushButton { background: #1976d2; color: #fff; }
                QHeaderView::section { background: #1976d2; color: #fff; }
                QComboBox, QComboBox QAbstractItemView { background: #fff; color: #222; selection-background-color: #1976d2; selection-color: #fff; }
            """)
        elif theme_name == 'أخضر':
            app.setStyleSheet("""
                QWidget { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e8f5e9, stop:1 #43a047); color: #222; }
                QPushButton { background: #43a047; color: #fff; }
                QHeaderView::section { background: #43a047; color: #fff; }
                QComboBox, QComboBox QAbstractItemView { background: #fff; color: #222; selection-background-color: #43a047; selection-color: #fff; }
            """)
        elif theme_name == 'أحمر':
            app.setStyleSheet("""
                QWidget { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffebee, stop:1 #e53935); color: #222; }
                QPushButton { background: #e53935; color: #fff; }
                QHeaderView::section { background: #e53935; color: #fff; }
                QComboBox, QComboBox QAbstractItemView { background: #fff; color: #222; selection-background-color: #e53935; selection-color: #fff; }
            """)
        elif theme_name == 'رملي':
            app.setStyleSheet("""
                QWidget { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #fff8e1, stop:1 #ffe082); color: #222; }
                QPushButton { background: #ffe082; color: #795548; }
                QHeaderView::section { background: #ffe082; color: #795548; }
                QComboBox, QComboBox QAbstractItemView { background: #fff; color: #222; selection-background-color: #ffe082; selection-color: #795548; }
            """)

    def apply_font(self, font):
        from PyQt5.QtGui import QFont
        app = QtWidgets.QApplication.instance()
        font = font if isinstance(font, QFont) else QFont(font)
        app.setFont(font)
        # ضبط الخط تلقائياً حسب الاتجاه
        direction = app.layoutDirection()
        if direction == QtCore.Qt.RightToLeft:
            # خطوط عربية مناسبة
            font.setFamily('Cairo')
        else:
            # خطوط لاتينية مناسبة
            font.setFamily('Segoe UI')
        app.setFont(font)

    def apply_direction(self):
        app = QtWidgets.QApplication.instance()
        direction = self.direction_combo.currentIndex()
        if direction == 0:
            app.setLayoutDirection(QtCore.Qt.RightToLeft)
            # عند التغيير للـ RTL، استخدم خط عربي مناسب
            font = app.font()
            font.setFamily('Cairo')
            app.setFont(font)
        else:
            app.setLayoutDirection(QtCore.Qt.LeftToRight)
            # عند التغيير للـ LTR، استخدم خط لاتيني مناسب
            font = app.font()
            font.setFamily('Segoe UI')
            app.setFont(font)