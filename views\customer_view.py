# نافذة إدارة العملاء المتقدمة
# لإضافة وتعديل وحذف العملاء مع دعم الهاتف والرصيد والملاحظات
# تاريخ الإنشاء: 2025-05-09

from PyQt5 import QtWidgets, QtCore, QtGui
import sqlite3
from models import database
from utils.utilities import parse_permissions
from main import get_app_icon

class CustomerWindow(QtWidgets.QWidget):
    def __init__(self, permissions=None, labels=None):
        super().__init__()
        self.labels = labels or {
            'CUSTOMER_LABEL': 'عميل'
        }
        self.setWindowTitle(f"إدارة {self.labels['CUSTOMER_LABEL']}ين")
        self.setGeometry(450, 200, 700, 500)
        self.setWindowIcon(get_app_icon())
        # حفظ الصلاحيات كقائمة
        self.permissions = parse_permissions(permissions)
        self.apply_custom_style()  # تطبيق الخطوط العصرية
        self.setup_ui()
        self.load_customers()

    def apply_custom_style(self):
        """
        تحميل وتطبيق ملف QSS الذي يحتوي على خطوط Google Fonts (Cairo, Amiri, ...)
        """
        try:
            with open('resources/styles/app_style.qss', encoding='utf-8') as f:
                self.setStyleSheet(f.read())
        except Exception as e:
            print(f"تعذر تحميل ملف QSS: {e}")

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout()
        # شريط البحث
        self.search_edit = QtWidgets.QLineEdit()
        self.search_edit.setPlaceholderText(f"ابحث باسم أو هاتف {self.labels['CUSTOMER_LABEL']}...")
        self.search_edit.textChanged.connect(self.filter_customers)
        layout.addWidget(self.search_edit)
        form = QtWidgets.QFormLayout()
        # اسم العميل
        self.name_edit = QtWidgets.QLineEdit()
        form.addRow(f"اسم {self.labels['CUSTOMER_LABEL']}:", self.name_edit)
        # رقم الهاتف
        self.phone_edit = QtWidgets.QLineEdit()
        form.addRow('رقم الهاتف:', self.phone_edit)
        # الرصيد
        self.balance_spin = QtWidgets.QDoubleSpinBox()
        self.balance_spin.setRange(-100000, 100000)
        self.balance_spin.setDecimals(2)
        form.addRow('الرصيد:', self.balance_spin)
        # المستحق/الرصيد السالب (عرض فقط)
        self.due_label = QtWidgets.QLabel('0.00')
        self.due_label.setStyleSheet('color: red; font-weight: bold;')
        form.addRow('المستحق/الرصيد السالب:', self.due_label)
        # ملاحظات
        self.notes_edit = QtWidgets.QLineEdit()
        form.addRow('ملاحظات:', self.notes_edit)
        layout.addLayout(form)
        # أزرار
        btns = QtWidgets.QHBoxLayout()
        self.add_btn = QtWidgets.QPushButton(f"إضافة {self.labels['CUSTOMER_LABEL']}")
        self.add_btn.clicked.connect(self.add_customer)
        btns.addWidget(self.add_btn)
        self.edit_btn = QtWidgets.QPushButton('تعديل')
        self.edit_btn.clicked.connect(self.edit_customer)
        btns.addWidget(self.edit_btn)
        self.delete_btn = QtWidgets.QPushButton('حذف')
        self.delete_btn.clicked.connect(self.delete_customer)
        btns.addWidget(self.delete_btn)
        
        # زر عرض تقارير العملاء
        self.report_btn = QtWidgets.QPushButton('عرض تقارير العملاء')
        self.report_btn.setStyleSheet('background-color: #455a64; color: white; font-weight: bold;')
        self.report_btn.clicked.connect(self.show_customers_report_window)
        btns.addWidget(self.report_btn)
        # زر طباعة تقرير العملاء
        self.print_btn = QtWidgets.QPushButton('طباعة العملاء')
        self.print_btn.setStyleSheet('background-color: #4caf50; color: white; font-weight: bold;')
        self.print_btn.clicked.connect(self.print_current_customers)
        btns.addWidget(self.print_btn)
        # الأزرار تعمل دائماً بغض النظر عن الصلاحيات
        layout.addLayout(btns)
        # جدول العملاء
        self.table = QtWidgets.QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels(['الاسم', 'الهاتف', 'الرصيد', 'المستحق/الرصيد السالب', 'ملاحظات', ''])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.table.cellClicked.connect(self.fill_form_from_table)
        layout.addWidget(self.table)
        self.status_label = QtWidgets.QLabel('')
        layout.addWidget(self.status_label)
        self.setLayout(layout)

    def load_customers(self):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        # جلب الرصيد والمستحق
        c.execute('SELECT name, phone, balance, due_amount, notes FROM customers ORDER BY name')
        rows = c.fetchall()
        conn.close()
        self.all_customers = rows  # حفظ جميع العملاء للبحث
        self.display_customers(rows)

    def display_customers(self, rows):
        self.table.setRowCount(len(rows))
        for i, row in enumerate(rows):
            for j, val in enumerate(row):
                # تمييز الرصيد السالب أو المستحق باللون الأحمر
                item = QtWidgets.QTableWidgetItem(str(val))
                if j == 3 and float(val) < 0:
                    item.setForeground(QtGui.QColor('red'))
                self.table.setItem(i, j, item)
            # زر حذف سريع
            del_btn = QtWidgets.QPushButton('حذف')
            del_btn.clicked.connect(lambda _, r=i: self.delete_customer_row(r))
            self.table.setCellWidget(i, 5, del_btn)

    def add_customer(self):
        name = self.name_edit.text().strip()
        phone = self.phone_edit.text().strip()
        balance = self.balance_spin.value()
        notes = self.notes_edit.text().strip()
        if not name:
            self.status_label.setText('يرجى إدخال اسم العميل')
            self.status_label.setStyleSheet('color: red')
            return
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        # تحقق من عدم وجود اسم عميل مكرر
        c.execute('SELECT COUNT(*) FROM customers WHERE name = ?', (name,))
        if c.fetchone()[0] > 0:
            self.status_label.setText('اسم العميل مستخدم مسبقًا')
            self.status_label.setStyleSheet('color: red')
            conn.close()
            return
        try:
            c.execute('INSERT INTO customers (name, phone, balance, notes) VALUES (?, ?, ?, ?)',
                      (name, phone, balance, notes))
            conn.commit()
            self.status_label.setText('تمت إضافة العميل بنجاح')
            self.status_label.setStyleSheet('color: green')
            self.load_customers()
        except sqlite3.IntegrityError:
            self.status_label.setText('اسم العميل مستخدم مسبقًا')
            self.status_label.setStyleSheet('color: red')
        finally:
            conn.close()
        self.clear_form()

    def edit_customer(self):
        row = self.table.currentRow()
        if row < 0:
            self.status_label.setText('يرجى اختيار عميل للتعديل')
            self.status_label.setStyleSheet('color: red')
            return
        name = self.name_edit.text().strip()
        phone = self.phone_edit.text().strip()
        balance = self.balance_spin.value()
        notes = self.notes_edit.text().strip()
        old_name = self.table.item(row, 0).text()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        # تحقق من عدم وجود اسم عميل مكرر (عدا الاسم القديم)
        if name != old_name:
            c.execute('SELECT COUNT(*) FROM customers WHERE name = ?', (name,))
            if c.fetchone()[0] > 0:
                self.status_label.setText('اسم العميل مستخدم مسبقًا')
                self.status_label.setStyleSheet('color: red')
                conn.close()
                return
        # جلب الرصيد والمستحق القديم
        c.execute('SELECT balance, due_amount FROM customers WHERE name=?', (old_name,))
        row_data = c.fetchone()
        old_balance = row_data[0] if row_data else 0
        old_due = row_data[1] if row_data else 0
        # إذا زاد الرصيد، يتم تغطية المستحق أولاً
        delta = balance - old_balance
        new_due = old_due
        if delta > 0 and old_due < 0:
            # العميل أضاف رصيد
            cover = min(abs(old_due), delta)
            new_due = old_due + cover
            balance = balance - cover
        c.execute('UPDATE customers SET name=?, phone=?, balance=?, due_amount=?, notes=? WHERE name=?',
                  (name, phone, balance, new_due, notes, old_name))
        conn.commit()
        conn.close()
        self.status_label.setText('تم تعديل العميل')
        self.status_label.setStyleSheet('color: green')
        self.load_customers()
        self.clear_form()

    def delete_customer(self):
        row = self.table.currentRow()
        if row < 0:
            self.status_label.setText('يرجى اختيار عميل للحذف')
            self.status_label.setStyleSheet('color: red')
            return
        name = self.table.item(row, 0).text()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('DELETE FROM customers WHERE name=?', (name,))
        conn.commit()
        conn.close()
        self.status_label.setText('تم حذف العميل')
        self.status_label.setStyleSheet('color: green')
        self.load_customers()
        self.clear_form()

    def delete_customer_row(self, row):
        name = self.table.item(row, 0).text()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('DELETE FROM customers WHERE name=?', (name,))
        conn.commit()
        conn.close()
        self.status_label.setText('تم حذف العميل')
        self.status_label.setStyleSheet('color: green')
        self.load_customers()
        self.clear_form()

    def fill_form_from_table(self, row, col):
        self.name_edit.setText(self.table.item(row, 0).text())
        self.phone_edit.setText(self.table.item(row, 1).text())
        self.balance_spin.setValue(float(self.table.item(row, 2).text()))
        self.due_label.setText(self.table.item(row, 3).text())
        self.notes_edit.setText(self.table.item(row, 4).text())

    def clear_form(self):
        self.name_edit.clear()
        self.phone_edit.clear()
        self.balance_spin.setValue(0)
        self.due_label.setText('0.00')
        self.notes_edit.clear()

    def filter_customers(self):
        query = self.search_edit.text().strip()
        if not hasattr(self, 'all_customers'):
            return
        if not query:
            filtered = self.all_customers
        else:
            filtered = [row for row in self.all_customers if query in row[0] or query in row[1]]
        self.display_customers(filtered)

    def show_customers_report_window(self):
        """
        عرض نافذة تقارير التغييرات في العملاء.
        """
        report_window = QtWidgets.QDialog(self)
        report_window.setWindowTitle('تقارير التغييرات - العملاء')
        report_window.setGeometry(500, 250, 700, 400)
        layout = QtWidgets.QVBoxLayout()
        label = QtWidgets.QLabel('سجل التغييرات في العملاء:')
        label.setStyleSheet('font-size: 14pt; font-weight: bold; color: #263238;')
        layout.addWidget(label)
        table = QtWidgets.QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(['العملية', 'اسم العميل', 'الكمية', 'المستخدم', 'التاريخ'])
        table.horizontalHeader().setStretchLastSection(True)
        # جلب البيانات من جدول log إذا كان موجوداً
        try:
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('''SELECT action, customer_name, quantity, user, date FROM log WHERE section = ? ORDER BY date DESC LIMIT 50''', ('customers',))
            rows = c.fetchall()
            conn.close()
            table.setRowCount(len(rows))
            for row_idx, row_data in enumerate(rows):
                for col_idx, value in enumerate(row_data):
                    table.setItem(row_idx, col_idx, QtWidgets.QTableWidgetItem(str(value)))
        except Exception as e:
            table.setRowCount(1)
            table.setItem(0, 0, QtWidgets.QTableWidgetItem('لا يوجد سجل أو قاعدة بيانات التقارير غير مفعلة'))
        layout.addWidget(table)
        close_btn = QtWidgets.QPushButton('إغلاق')
        close_btn.clicked.connect(report_window.close)
        layout.addWidget(close_btn)
        report_window.setLayout(layout)
        report_window.exec_()

    def print_current_customers(self):
        from utils.print_utils import print_invoice
        html = self.generate_customers_html()
        print_invoice(html, self)

    def generate_customers_html(self):
        items_html = ""
        for row in self.get_customers_rows():
            items_html += f"<tr>{''.join(f'<td>{cell}</td>' for cell in row)}</tr>"
        html = f"""
        <html><head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}}</style></head><body dir='rtl'>
        <h2 style='text-align:center'>تقرير العملاء</h2>
        <table>{items_html}</table></body></html>"""
        return html

    def get_customers_rows(self):
        """
        جلب بيانات العملاء من الجدول كقائمة من الصفوف.
        """
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name, phone, balance, due_amount, notes FROM customers ORDER BY name')
        rows = c.fetchall()
        conn.close()
        return rows
