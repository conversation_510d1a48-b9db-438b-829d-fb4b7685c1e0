# نافذة تفعيل البرنامج (تظهر فقط عند التشغيل الأول أو انتهاء الصلاحية)
from PyQt5 import QtWidgets, QtCore
from utils.activation_core import check_owner_code, save_activation_period

class ActivationWindow(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('تفعيل البرنامج')
        self.setModal(True)
        self.setFixedSize(400, 300)
        self.init_ui()
        self.result = False

    def init_ui(self):
        layout = QtWidgets.QVBoxLayout(self)
        self.stacked = QtWidgets.QStackedWidget()
        # الصفحة الأولى: إدخال الرمز السري
        page1 = QtWidgets.QWidget()
        v1 = QtWidgets.QVBoxLayout(page1)
        v1.addWidget(QtWidgets.QLabel('يرجى إدخال رمز المالك لتفعيل البرنامج:'))
        self.code_edit = QtWidgets.QLineEdit()
        self.code_edit.setEchoMode(QtWidgets.QLineEdit.Password)
        v1.addWidget(self.code_edit)
        self.code_error = QtWidgets.QLabel()
        self.code_error.setStyleSheet('color:red')
        v1.addWidget(self.code_error)
        self.next_btn = QtWidgets.QPushButton('التالي')
        self.next_btn.clicked.connect(self.check_code)
        v1.addWidget(self.next_btn)
        v1.addStretch()
        self.stacked.addWidget(page1)
        # الصفحة الثانية: إدخال التواريخ
        page2 = QtWidgets.QWidget()
        v2 = QtWidgets.QVBoxLayout(page2)
        v2.addWidget(QtWidgets.QLabel('حدد فترة صلاحية البرنامج:'))
        form = QtWidgets.QFormLayout()
        self.start_date = QtWidgets.QDateEdit(QtCore.QDate.currentDate())
        self.start_date.setDisplayFormat('yyyy/MM/dd')
        self.start_date.setCalendarPopup(True)
        self.end_date = QtWidgets.QDateEdit(QtCore.QDate.currentDate().addMonths(1))
        self.end_date.setDisplayFormat('yyyy/MM/dd')
        self.end_date.setCalendarPopup(True)
        form.addRow('تاريخ البدء:', self.start_date)
        form.addRow('تاريخ الانتهاء:', self.end_date)
        v2.addLayout(form)
        self.date_error = QtWidgets.QLabel()
        self.date_error.setStyleSheet('color:red')
        v2.addWidget(self.date_error)
        self.save_btn = QtWidgets.QPushButton('حفظ وتفعيل')
        self.save_btn.clicked.connect(self.save_period)
        v2.addWidget(self.save_btn)
        v2.addStretch()
        self.stacked.addWidget(page2)
        layout.addWidget(self.stacked)

    def check_code(self):
        code = self.code_edit.text().strip()
        if not code:
            self.code_error.setText('الرجاء إدخال الرمز السري.')
            return
        if not check_owner_code(code):
            self.code_error.setText('رمز المالك غير صحيح!')
            return
        self.code_error.setText('')
        self.stacked.setCurrentIndex(1)

    def save_period(self):
        start = self.start_date.date().toString('yyyy/MM/dd')
        end = self.end_date.date().toString('yyyy/MM/dd')
        if end < start:
            self.date_error.setText('تاريخ الانتهاء يجب أن يكون بعد تاريخ البدء.')
            return
        try:
            save_activation_period(start, end)
        except Exception as e:
            self.date_error.setText(f'حدث خطأ أثناء حفظ التفعيل: {e}')
            return
        from utils import activation_core
        if not activation_core.is_period_valid():
            self.date_error.setText('فترة التفعيل غير صالحة. تحقق من التواريخ.')
            return
        QtWidgets.QMessageBox.information(self, 'تم التفعيل', 'تم تفعيل البرنامج بنجاح!')
        self.result = True
        self.accept()
