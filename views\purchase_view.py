# نافذة المشتريات الجديدة
# لإضافة وتعديل وحذف عمليات الشراء وتحديث المخزون بشكل احترافي
# تاريخ الإنشاء: 2025-06-06

from PyQt5 import QtWidgets, QtCore, QtGui
import sqlite3
from models import database
from utils.event_bus import event_bus
from main import get_app_icon

class PurchaseWindow(QtWidgets.QWidget):
    def mouseDoubleClickEvent(self, event):
        # تفعيل التعديل فقط إذا كان النقر على جدول المشتريات
        if self.purchases_table.viewport().rect().contains(self.purchases_table.mapFromParent(event.pos())):
            index = self.purchases_table.indexAt(self.purchases_table.mapFromParent(event.pos()))
            if index.isValid() and index.column() == 4:  # عمود سعر البيع
                row = index.row()
                current_sale_price = self.purchases_table.item(row, 4).text()
                try:
                    current_sale_price = float(current_sale_price)
                except Exception:
                    current_sale_price = 0.0
                new_price, ok = QtWidgets.QInputDialog.getDouble(self, 'تعديل سعر البيع', 'أدخل سعر البيع الجديد:', current_sale_price, 0.01, 100000, 2)
                if ok:
                    self.purchases_table.item(row, 4).setText(str(new_price))
                    self.status_label.setText('تم تعديل السعر في الجدول. اضغط "حفظ التعديلات" للحفظ النهائي.')
                    self.status_label.setStyleSheet('color: orange')
        super().mouseDoubleClickEvent(event)

    def save_sale_price_edits(self):
        # حفظ جميع تعديلات سعر البيع من الجدول إلى قاعدة البيانات
        updated = 0
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        for row in range(self.purchases_table.rowCount()):
            try:
                product = self.purchases_table.item(row, 0).text()
                supplier = self.purchases_table.item(row, 2).text()
                price = float(self.purchases_table.item(row, 3).text())
                date = self.purchases_table.item(row, 5).text()
                new_sale_price = float(self.purchases_table.item(row, 4).text())
                c.execute('''SELECT pu.id FROM purchases pu JOIN products p ON pu.product_id = p.id JOIN suppliers s ON pu.supplier_id = s.id WHERE p.name=? AND s.name=? AND pu.purchase_price=? AND pu.purchase_date=? LIMIT 1''', (product, supplier, price, date))
                row_data = c.fetchone()
                if row_data:
                    purchase_id = row_data[0]
                    c.execute('UPDATE purchases SET sale_price=? WHERE id=?', (new_sale_price, purchase_id))
                    updated += 1
            except Exception:
                continue
        conn.commit()
        conn.close()
        self.status_label.setText(f'✔ تم حفظ {updated} تعديل/تعديلات لسعر البيع بنجاح')
        self.status_label.setStyleSheet('color: green')
        self.load_purchases()
    # تم حذف النسخة المختصرة المكررة من setup_ui هنا نهائياً
    def edit_selected_purchase(self):
        row = self.purchases_table.currentRow()
        if row == -1:
            self.status_label.setText('يرجى اختيار عملية شراء من الجدول أولاً')
            self.status_label.setStyleSheet('color: red')
            return
        product = self.purchases_table.item(row, 0).text()
        quantity = int(self.purchases_table.item(row, 1).text())
        supplier = self.purchases_table.item(row, 2).text()
        price = float(self.purchases_table.item(row, 3).text())
        try:
            sale_price = float(self.purchases_table.item(row, 4).text())
        except Exception:
            sale_price = 0.0
        date = self.purchases_table.item(row, 5).text()

        self.product_combo.setCurrentText(product)
        self.quantity.setValue(quantity)
        self.supplier_combo.setCurrentText(supplier)
        self.purchase_price.setValue(price)
        self.sale_price.setValue(sale_price)
        self.purchase_date.setDate(QtCore.QDate.fromString(date, 'yyyy-MM-dd'))

        reply = QtWidgets.QMessageBox.question(self, 'تأكيد التعديل', 'هل تريد حفظ التعديلات على هذه العملية؟', QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No)
        if reply == QtWidgets.QMessageBox.Yes:
            try:
                conn = sqlite3.connect(database.DB_NAME)
                c = conn.cursor()
                c.execute('''SELECT pu.id, pu.product_id, pu.quantity FROM purchases pu JOIN products p ON pu.product_id = p.id JOIN suppliers s ON pu.supplier_id = s.id WHERE p.name=? AND s.name=? AND pu.purchase_price=? AND pu.purchase_date=? LIMIT 1''', (product, supplier, price, date))
                row_data = c.fetchone()
                if not row_data:
                    self.status_label.setText('تعذر العثور على العملية المحددة')
                    self.status_label.setStyleSheet('color: red')
                    conn.close()
                    return
                purchase_id, product_id, old_quantity = row_data
                new_quantity = self.quantity.value()
                new_price = self.purchase_price.value()
                new_sale_price = self.sale_price.value()
                new_date = self.purchase_date.date().toString('yyyy-MM-dd')
                if new_sale_price == 0.0:
                    margin = 0.2
                    new_sale_price = round(new_price * (1 + margin), 2)
                try:
                    c.execute('UPDATE purchases SET quantity=?, purchase_price=?, sale_price=?, purchase_date=? WHERE id=?', (new_quantity, new_price, new_sale_price, new_date, purchase_id))
                except sqlite3.OperationalError:
                    c.execute('UPDATE purchases SET quantity=?, purchase_price=?, purchase_date=? WHERE id=?', (new_quantity, new_price, new_date, purchase_id))
                if new_quantity != old_quantity:
                    diff = new_quantity - old_quantity
                    c.execute('UPDATE inventory SET quantity = quantity + ? WHERE product_id = ?', (diff, product_id))
                conn.commit()
                conn.close()
                self.status_label.setText('✔ تم تعديل العملية بنجاح')
                self.status_label.setStyleSheet('color: green')
                self.load_purchases()
            except Exception as e:
                self.status_label.setText(f'حدث خطأ أثناء التعديل: {str(e)}')
                self.status_label.setStyleSheet('color: red')
    def __init__(self, permissions=None):
        super().__init__()
        self.setWindowTitle('قسم - المشتريات')
        self.setGeometry(450, 200, 700, 500)
        self.setWindowIcon(get_app_icon())
        self.setup_ui()
        self.load_purchases()
        # الاشتراك في إشارات التحديث
        event_bus.product_added.connect(self.refresh_products)
        event_bus.product_updated.connect(self.refresh_products)
        event_bus.product_deleted.connect(self.refresh_products)
        event_bus.purchase_made.connect(self.load_purchases)
        event_bus.data_changed.connect(self._on_data_changed)

    def _on_data_changed(self, section):
        if section in ("purchase", "product"):
            self.load_purchases()
            self.refresh_products()

    def setup_ui(self):
        self.layout = QtWidgets.QVBoxLayout()
        form = QtWidgets.QFormLayout()
        # اسم المنتج
        self.product_combo = QtWidgets.QComboBox()
        self.refresh_products()
        form.addRow('اسم المنتج:', self.product_combo)
        # الكمية
        self.quantity = QtWidgets.QSpinBox()
        self.quantity.setRange(1, 100000)
        form.addRow('الكمية:', self.quantity)
        # اسم المورد (قائمة منسدلة مع بحث)
        self.supplier_combo = QtWidgets.QComboBox()
        self.supplier_combo.setEditable(False)
        self.supplier_combo.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.refresh_suppliers()
        self.supplier_combo.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.supplier_combo.view().setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.supplier_combo.setMaxVisibleItems(15)
        form.addRow('اسم المورد:', self.supplier_combo)
        # سعر الشراء
        self.purchase_price = QtWidgets.QDoubleSpinBox()
        self.purchase_price.setRange(0.01, 100000)
        self.purchase_price.setDecimals(2)
        form.addRow('سعر الشراء:', self.purchase_price)
        # سعر البيع (جديد)
        self.sale_price = QtWidgets.QDoubleSpinBox()
        self.sale_price.setRange(0.0, 100000)
        self.sale_price.setDecimals(2)
        self.sale_price.setToolTip('اتركه فارغاً ليتم حسابه تلقائياً')
        form.addRow('سعر البيع:', self.sale_price)
        # تاريخ الشراء
        self.purchase_date = QtWidgets.QDateEdit(QtCore.QDate.currentDate())
        self.purchase_date.setCalendarPopup(True)
        form.addRow('تاريخ الشراء:', self.purchase_date)

        self.layout.addLayout(form)

        self.add_btn = QtWidgets.QPushButton('إضافة عملية شراء')
        self.add_btn.clicked.connect(self.add_purchase)

        # زر حفظ تعديلات سعر البيع
        self.save_sale_price_btn = QtWidgets.QPushButton('حفظ تعديلات سعر البيع')
        self.save_sale_price_btn.setStyleSheet('background-color: #1976d2; color: white; font-weight: bold;')
        self.save_sale_price_btn.clicked.connect(self.save_sale_price_edits)
        self.layout.addWidget(self.add_btn)
        self.layout.addWidget(self.save_sale_price_btn)

        self.print_btn = QtWidgets.QPushButton('طباعة السند')
        self.print_btn.clicked.connect(self.print_current_receipt)
        self.layout.addWidget(self.print_btn)

        self.purchases_table = QtWidgets.QTableWidget()
        self.purchases_table.setColumnCount(6)
        self.purchases_table.setHorizontalHeaderLabels(['اسم المنتج', 'الكمية', 'المورد', 'سعر الشراء', 'سعر البيع', 'تاريخ الشراء'])
        self.layout.addWidget(self.purchases_table)

        self.status_label = QtWidgets.QLabel('')
        self.layout.addWidget(self.status_label)

        self.setLayout(self.layout)

        self.product_combo.activated.connect(lambda: self.quantity.setFocus())
        self.quantity.editingFinished.connect(lambda: self.supplier_combo.setFocus())
        self.supplier_combo.activated.connect(lambda: self.purchase_price.setFocus())
        self.purchase_price.editingFinished.connect(lambda: self.sale_price.setFocus())
        self.sale_price.editingFinished.connect(lambda: self.purchase_date.setFocus())
        self.purchase_date.editingFinished.connect(self.add_btn.setFocus)
        self.purchase_date.returnPressed = self.add_purchase
        self.add_btn.setAutoDefault(True)
        self.add_btn.setDefault(True)
    def refresh_suppliers(self):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name FROM suppliers ORDER BY name')
        suppliers = c.fetchall()
        conn.close()
        self.supplier_combo.clear()
        for s in suppliers:
            self.supplier_combo.addItem(s[0])
        # تحديث QCompleter بعد تحديث القائمة
        if hasattr(self, 'supplier_completer'):
            self.supplier_completer.model().setStringList([self.supplier_combo.itemText(i) for i in range(self.supplier_combo.count())])

    def refresh_products(self):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name FROM products ORDER BY name')
        products = c.fetchall()
        conn.close()
        self.product_combo.clear()
        for p in products:
            self.product_combo.addItem(p[0])

    def load_purchases(self):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        try:
            c.execute('''SELECT p.name, pu.quantity, s.name, pu.purchase_price, pu.sale_price, pu.purchase_date FROM purchases pu JOIN products p ON pu.product_id = p.id JOIN suppliers s ON pu.supplier_id = s.id ORDER BY pu.id DESC LIMIT 50''')
            rows = c.fetchall()
        except sqlite3.OperationalError:
            c.execute('''SELECT p.name, pu.quantity, s.name, pu.purchase_price, NULL, pu.purchase_date FROM purchases pu JOIN products p ON pu.product_id = p.id JOIN suppliers s ON pu.supplier_id = s.id ORDER BY pu.id DESC LIMIT 50''')
            rows = c.fetchall()
        conn.close()
        self.purchases_table.setRowCount(len(rows))
        for i, row in enumerate(rows):
            for j, val in enumerate(row):
                self.purchases_table.setItem(i, j, QtWidgets.QTableWidgetItem(str(val) if val is not None else ''))

    def add_purchase(self):
        self.add_btn.setEnabled(False)
        try:
            if hasattr(self, 'permissions'):
                if 'add_purchase' not in self.permissions and not (hasattr(self, 'role') and self.role == 'purchaser'):
                    QtWidgets.QMessageBox.critical(self, 'صلاحية مفقودة', 'ليس لديك صلاحية للوصول إلى هذا القسم')
                    return
            product = self.product_combo.currentText().strip()
            quantity = self.quantity.value()
            supplier = self.supplier_combo.currentText().strip()
            price = self.purchase_price.value()
            sale_price = self.sale_price.value()
            date = self.purchase_date.date().toString('yyyy-MM-dd')
            if not product or not supplier or price <= 0 or quantity <= 0:
                self.status_label.setText('يرجى تعبئة جميع الحقول بشكل صحيح')
                self.status_label.setStyleSheet('color: red')
                return
            # --- منطق احترافي لمنع تكرار الشراء إذا كان المخزون كافياً ---
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('SELECT id FROM products WHERE name = ?', (product,))
            product_row = c.fetchone()
            if not product_row:
                self.status_label.setText('المنتج غير موجود')
                self.status_label.setStyleSheet('color: red')
                conn.close()
                return
            product_id = product_row[0]
            # جلب الكمية الحالية وحد إعادة الطلب (min_quantity)
            c.execute('SELECT IFNULL(i.quantity,0), p.min_quantity FROM products p LEFT JOIN inventory i ON p.id = i.product_id WHERE p.id = ?', (product_id,))
            inv_row = c.fetchone()
            current_qty = inv_row[0] if inv_row else 0
            min_qty = inv_row[1] if inv_row else 0
            # إذا الكمية الحالية أكبر من min_quantity، امنع الشراء أو اعرض تأكيد
            if current_qty > min_qty:
                reply = QtWidgets.QMessageBox.question(self, 'تنبيه مخزون', f'الكمية المتوفرة من المنتج "{product}" ({current_qty}) أكبر من الحد الأدنى ({min_qty}).\nهل أنت متأكد أنك تريد تنفيذ عملية الشراء؟', QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No, QtWidgets.QMessageBox.No)
                if reply != QtWidgets.QMessageBox.Yes:
                    self.status_label.setText('تم إلغاء عملية الشراء لوجود مخزون كافٍ')
                    self.status_label.setStyleSheet('color: orange')
                    conn.close()
                    return
            c.execute('SELECT id FROM suppliers WHERE name = ?', (supplier,))
            supplier_row = c.fetchone()
            if not supplier_row:
                self.status_label.setText('اسم المورد غير موجود. يرجى إضافته أولاً من قسم الموردين.')
                self.status_label.setStyleSheet('color: red')
                conn.close()
                return
            supplier_id = supplier_row[0]
            if sale_price == 0.0:
                margin = 0.2
                sale_price = round(price * (1 + margin), 2)
            try:
                c.execute('INSERT INTO purchases (product_id, supplier_id, quantity, purchase_price, sale_price, purchase_date) VALUES (?, ?, ?, ?, ?, ?)',
                          (product_id, supplier_id, quantity, price, sale_price, date))
            except sqlite3.OperationalError:
                c.execute('INSERT INTO purchases (product_id, supplier_id, quantity, purchase_price, purchase_date) VALUES (?, ?, ?, ?, ?)',
                          (product_id, supplier_id, quantity, price, date))
            # تحديث أو إضافة للمخزون
            c.execute('SELECT quantity FROM inventory WHERE product_id = ?', (product_id,))
            inv_row2 = c.fetchone()
            if inv_row2:
                c.execute('UPDATE inventory SET quantity = quantity + ? WHERE product_id = ?', (quantity, product_id))
            else:
                c.execute('INSERT INTO inventory (product_id, quantity, expiry_date) VALUES (?, ?, ?)', (product_id, quantity, ''))
            conn.commit()
            conn.close()
            self.status_label.setText('✔ تمت إضافة عملية الشراء بنجاح')
            self.status_label.setStyleSheet('color: green')
            self.load_purchases()
            self.refresh_products()
            event_bus.purchase_made.emit()
            event_bus.data_changed.emit("purchase")
            self.product_combo.setCurrentIndex(0)
            self.quantity.setValue(1)
            self.supplier_combo.setCurrentIndex(0)
            self.purchase_price.setValue(0.0)
            self.sale_price.setValue(0.0)
            self.purchase_date.setDate(QtCore.QDate.currentDate())
        except Exception as e:
            self.status_label.setText(f'حدث خطأ: {str(e)}')
            self.status_label.setStyleSheet('color: red')
        finally:
            self.add_btn.setEnabled(True)
        # تحديث قائمة الموردين عند فتح نافذة المشتريات
    def showEvent(self, event):
        super().showEvent(event)
        self.refresh_suppliers()
        if hasattr(self, 'purchase_price'):
            self.purchase_price.setValue(0.0)
        if hasattr(self, 'sale_price'):
            self.sale_price.setValue(0.0)
        if hasattr(self, 'purchase_date'):
            self.purchase_date.setDate(QtCore.QDate.currentDate())
        if hasattr(self, 'product_combo'):
            self.product_combo.setCurrentIndex(0)
        if hasattr(self, 'quantity'):
            self.quantity.setValue(1)
        if hasattr(self, 'supplier_combo'):
            self.supplier_combo.setCurrentIndex(0)
        if hasattr(self, 'status_label'):
            self.status_label.setText("")

    def print_current_receipt(self):
        """
        توليد HTML لسند المشتريات الحالي وطباعته باستخدام print_invoice.
        """
        from utils.print_utils import print_invoice
        html = self.generate_receipt_html()  # دالة تولد HTML للسند
        print_invoice(html, self)

    def generate_receipt_html(self):
        items_html = ""
        row_count = self.purchases_table.rowCount()
        col_count = self.purchases_table.columnCount()
        for i in range(row_count):
            items_html += "<tr>"
            for j in range(col_count):
                val = self.purchases_table.item(i, j)
                items_html += f"<td>{val.text() if val else ''}</td>"
            items_html += "</tr>"
        html = f"""
        <html>
        <head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}}</style></head>
        <body dir='rtl'>
        <h2 style='text-align:center'>سند مشتريات</h2>
        <table><tr><th>اسم المنتج</th><th>الكمية</th><th>المورد</th><th>سعر الشراء</th><th>سعر البيع</th><th>تاريخ الشراء</th></tr>
        {items_html}
        </table>
        </body></html>
        """
        return html