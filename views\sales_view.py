# نافذة المبيعات (نقطة البيع)
# لإدارة عمليات البيع والسلة وطباعة الفواتير
# تاريخ آخر تعديل: 2025-05-09

from PyQt5 import QtWidgets, QtGui, QtCore
from utils.event_bus import event_bus
import sqlite3
from models import database
from utils.utilities import parse_permissions, get_cogs_fifo
from main import get_app_icon

class SalesWindow(QtWidgets.QWidget):
    # الكلاس الرئيسي لنافذة المبيعات (نقطة البيع)
    def __init__(self, permissions=None, labels=None):
        super().__init__()
        self.labels = labels or {
            'PRODUCT_LABEL': 'منتج',
            'CUSTOMER_LABEL': 'عميل',
            'SUPPLIER_LABEL': 'مورد',
            'BUSINESS_TYPE': 'نشاط'
        }
        self.setWindowTitle(f"{self.labels.get('BUSINESS_TYPE', 'النشاط')} - نقطة البيع")
        self.setGeometry(400, 120, 1100, 650)
        # حفظ الصلاحيات كقائمة
        self.permissions = parse_permissions(permissions)
        self.cart = []
        self.setup_ui()
        self.setWindowIcon(get_app_icon())

    def setup_ui(self):
        main_layout = QtWidgets.QVBoxLayout(self)
        # رأس النافذة: اسم القسم واسم المستخدم وتاريخ اليوم
        header = QtWidgets.QHBoxLayout()
        section_label = QtWidgets.QLabel(f"قسم {self.labels.get('BUSINESS_TYPE', 'المبيعات')}")
        section_label.setStyleSheet('font-size: 18pt; font-weight: bold; color: #1976d2;')
        header.addWidget(section_label)
        header.addStretch()
        user_label = QtWidgets.QLabel('المستخدم الحالي')
        user_label.setStyleSheet('font-size: 12pt; color: #555;')
        header.addWidget(user_label)
        date_label = QtWidgets.QLabel(QtCore.QDate.currentDate().toString('yyyy-MM-dd'))
        date_label.setStyleSheet('font-size: 12pt; color: #1976d2; margin-right: 16px;')
        header.addWidget(date_label)
        main_layout.addLayout(header)

        # عمود المنتجات
        split_layout = QtWidgets.QHBoxLayout()
        left_col = QtWidgets.QVBoxLayout()
        search_bar = QtWidgets.QHBoxLayout()
        self.search_input = QtWidgets.QLineEdit()
        self.search_input.setPlaceholderText(f"ابحث عن {self.labels['PRODUCT_LABEL']}...")
        self.search_input.textChanged.connect(self.search_products)
        search_bar.addWidget(self.search_input)
        self.barcode_input = QtWidgets.QLineEdit()
        self.barcode_input.setPlaceholderText(f"باركود {self.labels['PRODUCT_LABEL']}")
        self.barcode_input.returnPressed.connect(self.handle_barcode)
        search_bar.addWidget(self.barcode_input)
        left_col.addLayout(search_bar)
        self.products_list = QtWidgets.QListWidget()
        self.products_list.itemDoubleClicked.connect(self.add_to_cart_dialog)
        self.products_list.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.products_list.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.products_list.customContextMenuRequested.connect(self.show_product_context_menu)
        left_col.addWidget(self.products_list, stretch=1)
        from utils.print_utils import get_icon
        add_btn = QtWidgets.QPushButton(f"إضافة {self.labels['PRODUCT_LABEL']} للسلة")
        add_btn.setIcon(QtGui.QIcon(get_icon('add.png')))
        add_btn.setStyleSheet('background-color: #43a047; color: white; font-weight: bold;')
        add_btn.clicked.connect(self.add_product_dialog)
        left_col.addWidget(add_btn)
        split_layout.addLayout(left_col, 2)

        # عمود السلة
        right_col = QtWidgets.QVBoxLayout()
        self.cart_table = QtWidgets.QTableWidget()
        self.cart_table.setColumnCount(5)
        self.cart_table.setHorizontalHeaderLabels([
            self.labels['PRODUCT_LABEL'], 'الكمية', 'سعر البيع', 'الخصم', 'إجراء'])
        self.cart_table.horizontalHeader().setStretchLastSection(True)
        self.cart_table.setAlternatingRowColors(True)
        right_col.addWidget(self.cart_table, stretch=2)
        self.summary_label = QtWidgets.QLabel('')
        self.summary_label.setStyleSheet('font-size: 13pt; color: #1976d2; font-weight: bold;')
        right_col.addWidget(self.summary_label)
        btns = QtWidgets.QHBoxLayout()
        self.remove_btn = QtWidgets.QPushButton('حذف من السلة')
        self.remove_btn.setIcon(QtGui.QIcon(get_icon('delete.png')))
        self.remove_btn.setStyleSheet('background-color: #e53935; color: white; font-weight: bold;')
        self.remove_btn.clicked.connect(self.remove_from_cart)
        btns.addWidget(self.remove_btn)
        self.edit_btn = QtWidgets.QPushButton(f"تعديل {self.labels['PRODUCT_LABEL']} المحدد")
        self.edit_btn.setIcon(QtGui.QIcon(get_icon('edit.png')))
        self.edit_btn.setStyleSheet('background-color: #1976d2; color: white; font-weight: bold;')
        self.edit_btn.clicked.connect(self.edit_cart_item)
        btns.addWidget(self.edit_btn)
        self.finish_btn = QtWidgets.QPushButton('إنهاء البيع')
        self.finish_btn.setIcon(QtGui.QIcon(get_icon('sell.png')))
        self.finish_btn.setStyleSheet('background-color: #263238; color: white; font-size: 15pt; font-weight: bold;')
        self.finish_btn.clicked.connect(self.finish_sale)
        btns.addWidget(self.finish_btn)
        self.report_btn = QtWidgets.QPushButton('عرض تقارير المبيعات')
        self.report_btn.setStyleSheet('background-color: #455a64; color: white; font-weight: bold;')
        self.report_btn.clicked.connect(self.show_sales_report_window)
        btns.addWidget(self.report_btn)
        if 'edit' not in self.permissions:
            self.edit_btn.setEnabled(False)
        if 'delete' not in self.permissions:
            self.remove_btn.setEnabled(False)
        right_col.addLayout(btns)
        self.form_layout = QtWidgets.QFormLayout()
        self.customer_combo = QtWidgets.QComboBox()
        self.customer_combo.setEditable(True)
        self.customer_combo.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.refresh_customers()
        self.customer_combo.setCurrentIndex(-1)
        completer = QtWidgets.QCompleter([self.customer_combo.itemText(i) for i in range(self.customer_combo.count())])
        completer.setCaseSensitivity(QtCore.Qt.CaseInsensitive)
        completer.setCompletionMode(QtWidgets.QCompleter.PopupCompletion)
        self.customer_combo.setCompleter(completer)
        self.form_layout.addRow('اسم العميل:', self.customer_combo)
        right_col.addLayout(self.form_layout)
        self.status_label = QtWidgets.QLabel('')
        right_col.addWidget(self.status_label)
        split_layout.addLayout(right_col, 3)
        main_layout.addLayout(split_layout, 5)

        # تبويبات سجل البيع
        self.sales_tabs = QtWidgets.QTabWidget()
        self.sales_log = QtWidgets.QTableWidget()
        self.sales_log.setColumnCount(4)
        self.sales_log.setHorizontalHeaderLabels(['التاريخ', 'العميل', 'الإجمالي', 'طريقة الدفع'])
        self.sales_log.setMaximumHeight(120)
        self.sales_tabs.addTab(self.sales_log, 'الكل')
        self.sales_log_customers = QtWidgets.QTableWidget()
        self.sales_log_customers.setColumnCount(4)
        self.sales_log_customers.setHorizontalHeaderLabels(['التاريخ', 'العميل', 'الإجمالي', 'طريقة الدفع'])
        self.sales_log_customers.setMaximumHeight(120)
        self.sales_tabs.addTab(self.sales_log_customers, 'عملاء فعليين')
        self.sales_log_others = QtWidgets.QTableWidget()
        self.sales_log_others.setColumnCount(4)
        self.sales_log_others.setHorizontalHeaderLabels(['التاريخ', 'العميل', 'الإجمالي', 'طريقة الدفع'])
        self.sales_log_others.setMaximumHeight(120)
        self.sales_tabs.addTab(self.sales_log_others, 'غير العملاء')
        main_layout.addWidget(self.sales_tabs)
        # زر إرجاع منتج
        self.return_btn = QtWidgets.QPushButton('إرجاع منتج')
        self.return_btn.setIcon(QtGui.QIcon(get_icon('delete.png')))
        self.return_btn.setStyleSheet('background-color: #fb8c00; color: white; font-weight: bold;')
        self.return_btn.clicked.connect(self.open_return_dialog)
        main_layout.addWidget(self.return_btn)
        # زر طباعة الفاتورة
        self.print_btn = QtWidgets.QPushButton('طباعة الفاتورة')
        self.print_btn.setIcon(QtGui.QIcon(get_icon('smart.ico')))
        self.print_btn.setStyleSheet('background-color: #1976d2; color: white; font-weight: bold;')
        self.print_btn.clicked.connect(self.print_current_invoice)
        main_layout.addWidget(self.print_btn)
        self.setLayout(main_layout)
        self.load_products()
        self.refresh_cart()
        self.load_sales_log()
        self.products_list.installEventFilter(self)
        self.cart_table.installEventFilter(self)

    def refresh_customers(self):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name FROM customers ORDER BY name')
        customers = c.fetchall()
        conn.close()
        self.customer_combo.clear()
        # لا نضيف عنصر فارغ، فقط قائمة العملاء
        for cst in customers:
            # label = الاسم للعرض، userData = الاسم الأصلي (بدون معالجة)
            self.customer_combo.addItem(cst[0], cst[0])
        self.customer_combo.setCurrentIndex(-1)  # إبقاء الحقل فارغًا افتراضيًا
        # تحديث الـ QCompleter بعد التحديث
        completer = QtWidgets.QCompleter([self.customer_combo.itemText(i) for i in range(self.customer_combo.count())])
        completer.setCaseSensitivity(QtCore.Qt.CaseInsensitive)
        completer.setCompletionMode(QtWidgets.QCompleter.PopupCompletion)
        self.customer_combo.setCompleter(completer)

    def eventFilter(self, obj, event):
        if obj == self.products_list and event.type() == QtCore.QEvent.KeyPress:
            if event.key() == QtCore.Qt.Key_Return or event.key() == QtCore.Qt.Key_Enter:
                item = self.products_list.currentItem()
                if item:
                    self.add_to_cart_dialog(item)
                return True
        if obj == self.cart_table and event.type() == QtCore.QEvent.KeyPress:
            if event.key() == QtCore.Qt.Key_Delete:
                self.remove_from_cart()
                return True
        return super().eventFilter(obj, event)

    def show_product_context_menu(self, pos):
        item = self.products_list.itemAt(pos)
        if item:
            menu = QtWidgets.QMenu()
            add_action = menu.addAction('إضافة للسلة')
            action = menu.exec_(self.products_list.mapToGlobal(pos))
            if action == add_action:
                self.add_to_cart_dialog(item)

    def load_products(self):
        self.products_list.clear()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('''SELECT p.name, IFNULL(i.quantity, 0), p.min_quantity FROM products p LEFT JOIN inventory i ON p.id = i.product_id ORDER BY p.name''')
        for name, qty, min_qty in c.fetchall():
            display_name = name
            item = QtWidgets.QListWidgetItem(display_name)
            if qty <= min_qty:
                item.setForeground(QtGui.QColor('red'))
                item.setText(f'{display_name} ⚠️')
            self.products_list.addItem(item)
        conn.close()

    def search_products(self):
        text = self.search_input.text().strip()
        self.products_list.clear()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name FROM products WHERE name LIKE ?', (f'%{text}%',))
        for row in c.fetchall():
            display_name = row[0]
            self.products_list.addItem(display_name)
        conn.close()

    def get_smart_profit_percent(self, avg_unit_cost, qty, product_name=None):
        """
        سياسة ذكية ومحسنة لتحديد نسبة الربح تراعي: سعر المنتج، الكمية، المخزون، الفئة، سعر السوق، وتاريخ الصلاحية.
        تم رفع النسب لتكون أكثر واقعية.
        """
        import sqlite3
        from datetime import datetime, timedelta
        percent = 0.22  # نسبة افتراضية أعلى من السابق
        # منطق النسبة الأساسي حسب التكلفة
        if avg_unit_cost > 1000:
            percent = 0.13  # منتجات غالية
        elif avg_unit_cost < 50:
            percent = 0.38  # منتجات رخيصة
        elif qty > 5:
            percent = 0.16  # جملة
        else:
            percent = 0.28  # تجزئة
        # جلب بيانات إضافية من قاعدة البيانات إذا توفر اسم المنتج
        if product_name:
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            # تعديل: جلب expiry_date من جدول inventory وليس products
            c.execute('SELECT IFNULL(i.quantity,0), p.category, p.market_price, i.expiry_date FROM products p LEFT JOIN inventory i ON p.id = i.product_id WHERE p.name = ?', (product_name,))
            row = c.fetchone()
            conn.close()
            stock = row[0] if row else 0
            category = row[1] if row else ''
            market_price = row[2] if row else None
            expiry_date = row[3] if row else None
            # تعديل حسب المخزون
            if stock <= 2:
                percent += 0.07  # رفع النسبة عند ندرة المخزون
            elif stock >= 50:
                percent -= 0.06  # خفض النسبة عند وفرة المخزون
            # تعديل حسب سعر السوق
            if market_price and avg_unit_cost * (1 + percent) > market_price:
                percent = max((market_price / avg_unit_cost) - 1, 0.10)
            # تعديل حسب الفئة
            if category in ['إلكترونيات', 'أجهزة']:
                percent = max(percent, 0.15)
            elif category in ['مواد غذائية', 'مشروبات']:
                percent = min(percent, 0.25)
            # تعديل حسب تاريخ الصلاحية
            if expiry_date:
                try:
                    exp = datetime.strptime(expiry_date, '%Y-%m-%d')
                    if exp - datetime.now() < timedelta(days=30):
                        percent = min(percent, 0.13)
                except:
                    pass
        # ضمان أن النسبة ضمن حدود منطقية
        percent = max(0.10, min(percent, 0.45))
        return percent

    def add_product_dialog(self):
        # نافذة إضافة منتج للسلة مع جلب سعر البيع من آخر عملية شراء
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle('إضافة منتج للسلة')
        form = QtWidgets.QFormLayout(dialog)
        # اختيار المنتج
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name FROM products ORDER BY name')
        products = [row[0] for row in c.fetchall()]
        conn.close()
        product_combo = QtWidgets.QComboBox()
        product_combo.addItems(products)
        form.addRow('المنتج:', product_combo)
        qty = QtWidgets.QSpinBox()
        qty.setRange(1, 10000)
        form.addRow('الكمية:', qty)
        # تكلفة الشراء الفعلية للكمية
        purchase_cost_label = QtWidgets.QLabel('')
        def update_cost():
            pname = product_combo.currentText()
            q = qty.value()
            total_purchase_cost, avg_unit_cost = self.get_purchase_cost_fifo(pname, q)
            purchase_cost_label.setText(f"تكلفة الشراء للكمية: {total_purchase_cost:.2f} ر.س (متوسط للقطعة: {avg_unit_cost:.2f})")
        product_combo.currentIndexChanged.connect(update_cost)
        qty.valueChanged.connect(update_cost)
        update_cost()
        form.addRow(purchase_cost_label)
        # سعر البيع (جلب آخر سعر بيع من المشتريات)
        price = QtWidgets.QDoubleSpinBox()
        price.setRange(0.01, 100000)
        price.setDecimals(2)
        def set_default_price():
            pname = product_combo.currentText()
            q = qty.value()
            # جلب آخر سعر بيع من المشتريات
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('''SELECT sale_price FROM purchases JOIN products ON purchases.product_id = products.id WHERE products.name = ? AND sale_price IS NOT NULL AND sale_price > 0 ORDER BY purchases.id DESC LIMIT 1''', (pname,))
            row = c.fetchone()
            conn.close()
            if row and row[0]:
                price.setValue(float(row[0]))
            else:
                # fallback: حساب السعر الذكي
                _, avg_unit_cost = self.get_purchase_cost_fifo(pname, q)
                profit_percent = self.get_smart_profit_percent(avg_unit_cost, q, pname)
                price.setValue(round(avg_unit_cost * (1 + profit_percent), 2))
        product_combo.currentIndexChanged.connect(set_default_price)
        qty.valueChanged.connect(set_default_price)
        set_default_price()
        form.addRow('سعر البيع (يمكنك تعديله):', price)
        discount = QtWidgets.QDoubleSpinBox()
        discount.setRange(0, 100000)
        discount.setDecimals(2)
        form.addRow('الخصم:', discount)
        btns = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel)
        form.addRow(btns)
        btns.accepted.connect(dialog.accept)
        btns.rejected.connect(dialog.reject)
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            pname = product_combo.currentText()
            q = qty.value()
            p = price.value()
            d = discount.value()
            if d > q * p:
                QtWidgets.QMessageBox.warning(self, 'خطأ في الخصم', 'قيمة الخصم أكبر من إجمالي سعر المنتج. يرجى إدخال خصم صحيح.')
                return
            self.cart.append({'product': pname, 'quantity': q, 'price': p, 'discount': d})
            self.refresh_cart()

    def add_to_cart_dialog(self, item):
        product = item.text().replace(' ⚠️', '')
        available_qty = self.get_product_available_qty(product)
        if (available_qty == 0):
            QtWidgets.QMessageBox.warning(self, 'تنبيه', f'المنتج "{product}" غير متوفر في المخزون.')
            return
        qty, ok = QtWidgets.QInputDialog.getInt(self, 'كمية البيع', f'أدخل الكمية المطلوبة لـ {product} (المتوفر: {available_qty}):', 1, 1, available_qty)
        if ok:
            total_purchase_cost, avg_unit_cost = self.get_purchase_cost_fifo(product, qty)
            # جلب آخر سعر بيع من المشتريات
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('''SELECT sale_price FROM purchases JOIN products ON purchases.product_id = products.id WHERE products.name = ? AND sale_price IS NOT NULL AND sale_price > 0 ORDER BY purchases.id DESC LIMIT 1''', (product,))
            row = c.fetchone()
            conn.close()
            if row and row[0]:
                suggested_price = float(row[0])
                price_msg = f"تكلفة الشراء للكمية: {total_purchase_cost:.2f} ر.س (متوسط للقطعة: {avg_unit_cost:.2f})\nآخر سعر بيع مدخل: {suggested_price:.2f} ر.س"
            else:
                profit_percent = self.get_smart_profit_percent(avg_unit_cost, qty, product)
                suggested_price = round(avg_unit_cost * (1 + profit_percent), 2)
                price_msg = f"تكلفة الشراء للكمية: {total_purchase_cost:.2f} ر.س (متوسط للقطعة: {avg_unit_cost:.2f})\nسعر البيع المقترح للقطعة ({int(profit_percent*100)}% ربح): {suggested_price:.2f} ر.س"
            price, ok2 = QtWidgets.QInputDialog.getDouble(self, 'سعر البيع', price_msg, suggested_price, 0.01, 100000, 2)
            if ok2:
                discount, ok3 = QtWidgets.QInputDialog.getDouble(self, 'خصم على المنتج', f'أدخل قيمة الخصم (ريال) لـ {product}:', 0, 0, price*qty, 2)
                if ok3:
                    self.cart.append({'product': product, 'quantity': qty, 'price': price, 'discount': discount})
                    self.refresh_cart()

    def get_purchase_cost_fifo(self, product, qty):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        # جلب معرف المنتج
        c.execute('SELECT id FROM products WHERE name = ?', (product,))
        row = c.fetchone()
        if not row:
            conn.close()
            return 0.0, 0.0
        product_id = row[0]
        # جلب الدفعات حسب الأقدم فالأحدث
        c.execute('''SELECT quantity, purchase_price FROM purchases WHERE product_id = ? ORDER BY purchase_date ASC, id ASC''', (product_id,))
        batches = c.fetchall()
        conn.close()
        needed = qty
        total_cost = 0.0
        total_units = 0
        for batch_qty, batch_price in batches:
            take = min(batch_qty, needed)
            total_cost += take * batch_price
            total_units += take
            needed -= take
            if needed <= 0:
                break
        avg_unit_cost = (total_cost / total_units) if total_units else 0.0
        return total_cost, avg_unit_cost

    def get_product_available_qty(self, product):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT i.quantity FROM products p LEFT JOIN inventory i ON p.id = i.product_id WHERE p.name = ?', (product,))
        row = c.fetchone()
        conn.close()
        return row[0] if row and row[0] is not None else 0

    def edit_cart_item(self):
        row = self.cart_table.currentRow()
        if row < 0 or row >= len(self.cart):
            return
        item = self.cart[row]
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle('تعديل بيانات المنتج')
        form = QtWidgets.QFormLayout(dialog)
        qty = QtWidgets.QSpinBox()
        qty.setRange(1, 10000)
        qty.setValue(item['quantity'])
        form.addRow('الكمية:', qty)
        price = QtWidgets.QDoubleSpinBox()
        price.setRange(0.01, 100000)
        price.setDecimals(2)
        price.setValue(item['price'])
        form.addRow('سعر البيع:', price)
        discount = QtWidgets.QDoubleSpinBox()
        discount.setRange(0, 100000)
        discount.setDecimals(2)
        discount.setValue(item.get('discount', 0))
        form.addRow('الخصم:', discount)
        btns = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel)
        form.addRow(btns)
        btns.accepted.connect(dialog.accept)
        btns.rejected.connect(dialog.reject)
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            new_qty = qty.value()
            new_price = price.value()
            new_discount = discount.value()
            if new_discount > new_qty * new_price:
                # رسالة عربية أصلية بدون أي معالجة نصية
                QtWidgets.QMessageBox.warning(self, 'خطأ في الخصم', 'قيمة الخصم أكبر من إجمالي سعر المنتج. يرجى إدخال خصم صحيح.')
                return
            item['quantity'] = new_qty
            item['price'] = new_price
            item['discount'] = new_discount
            self.refresh_cart()

    def get_product_price(self, product):
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT MAX(purchase_price) FROM purchases JOIN products ON purchases.product_id = products.id WHERE products.name = ?', (product,))
        row = c.fetchone()
        conn.close()
        return row[0] if row and row[0] else 0.0

    def calculate_cart_profit(self):
        """
        حساب إجمالي الربح في السلة: (سعر البيع - تكلفة الشراء الفعلي FIFO) لكل منتج
        """
        total_profit = 0.0
        for item in self.cart:
            pname = item['product']
            qty = item['quantity']
            sale_price = item['price']
            discount = item.get('discount', 0)
            # جلب معرف المنتج
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('SELECT id FROM products WHERE name = ?', (pname,))
            row = c.fetchone()
            conn.close()
            if not row:
                continue
            product_id = row[0]
            cogs = get_cogs_fifo(product_id, qty)
            profit = (sale_price * qty - discount) - cogs
            total_profit += profit
        return total_profit

    def refresh_cart(self):
        self.cart_table.setRowCount(len(self.cart))
        total_qty = 0
        total_price = 0
        total_discount = 0
        import functools
        for i, item in enumerate(self.cart):
            product_name = item['product']
            prod_item = QtWidgets.QTableWidgetItem(product_name)
            prod_item.setTextAlignment(QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
            prod_item.setData(QtCore.Qt.TextAlignmentRole, QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
            self.cart_table.setItem(i, 0, prod_item)
            qty_item = QtWidgets.QTableWidgetItem(str(item['quantity']))
            qty_item.setTextAlignment(QtCore.Qt.AlignCenter)
            qty_item.setData(QtCore.Qt.TextAlignmentRole, QtCore.Qt.AlignCenter)
            self.cart_table.setItem(i, 1, qty_item)
            price_item = QtWidgets.QTableWidgetItem(str(item['price']))
            price_item.setTextAlignment(QtCore.Qt.AlignCenter)
            price_item.setData(QtCore.Qt.TextAlignmentRole, QtCore.Qt.AlignCenter)
            self.cart_table.setItem(i, 2, price_item)
            discount_item = QtWidgets.QTableWidgetItem(str(item.get('discount', 0)))
            discount_item.setTextAlignment(QtCore.Qt.AlignCenter)
            discount_item.setData(QtCore.Qt.TextAlignmentRole, QtCore.Qt.AlignCenter)
            self.cart_table.setItem(i, 3, discount_item)
            del_btn = QtWidgets.QPushButton('حذف')
            del_btn.clicked.connect(functools.partial(self.remove_cart_row, i))
            self.cart_table.setCellWidget(i, 4, del_btn)
            total_qty += item['quantity']
            total_price += item['quantity'] * item['price']
            total_discount += item.get('discount', 0)
        total_profit = self.calculate_cart_profit()
        self.summary_label.setText(f'إجمالي الكمية: {total_qty} | إجمالي السعر: {total_price:.2f} ر.س | إجمالي الخصم: {total_discount:.2f} ر.س | إجمالي الربح: {total_profit:.2f} ر.س')

    def remove_cart_row(self, row):
        if 0 <= row < len(self.cart):
            del self.cart[row]
            self.refresh_cart()

    def remove_from_cart(self):
        row = self.cart_table.currentRow()
        if row >= 0:
            del self.cart[row]
            self.refresh_cart()

    def handle_barcode(self):
        barcode = self.barcode_input.text().strip()
        if not barcode:
            return
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT name FROM products WHERE name = ? OR category = ?', (barcode, barcode))
        row = c.fetchone()
        conn.close()
        if row:
            product = row[0]
            self.add_to_cart_dialog(QtWidgets.QListWidgetItem(product))
        else:
            # رسالة عربية أصلية بدون أي معالجة نصية
            QtWidgets.QMessageBox.warning(self, 'باركود غير موجود', 'لم يتم العثور على منتج بهذا الباركود.')
        self.barcode_input.clear()

    def finish_sale(self):
        if not self.cart:
            self.status_label.setText('السلة فارغة!')
            self.status_label.setStyleSheet('color: red')
            return
        payment_method, ok = QtWidgets.QInputDialog.getItem(self, 'طريقة الدفع', 'اختر طريقة الدفع:', ['نقدي', 'بطاقة', 'تحويل'], 0, False)
        if not ok:
            return
        invoice_discount, ok = QtWidgets.QInputDialog.getDouble(self, 'خصم على الفاتورة', 'أدخل قيمة الخصم على كامل الفاتورة (ريال):', 0, 0, 100000, 2)
        if not ok:
            invoice_discount = 0
        subtotal = sum(item['quantity'] * item['price'] - item.get('discount', 0) for item in self.cart)
        if invoice_discount > subtotal:
            # رسالة عربية أصلية بدون أي معالجة نصية
            QtWidgets.QMessageBox.warning(self, 'خطأ في الخصم', 'قيمة خصم الفاتورة أكبر من الإجمالي بعد خصومات المنتجات. يرجى إدخال خصم صحيح.')
            return
        total = subtotal - invoice_discount
        # استخدم البيانات الأصلية من QComboBox إذا توفرت
        customer = self.customer_combo.currentData()
        if not customer:
            customer = self.customer_combo.currentText().strip()
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        # تحقق من وجود العميل إذا لم يكن الحقل فارغًا
        customer_id = None
        customer_balance = None
        customer_due = None
        if customer:
            c.execute('SELECT id, balance, IFNULL(due_amount, 0) FROM customers WHERE name = ?', (customer,))
            customer_row = c.fetchone()
            if not customer_row:
                # رسالة عربية أصلية بدون أي معالجة نصية
                QtWidgets.QMessageBox.warning(self, 'عميل غير موجود', 'اسم العميل غير موجود. يرجى إضافته أولاً من قسم العملاء.')
                conn.close()
                return
            customer_id = customer_row[0]
            customer_balance = customer_row[1] if customer_row[1] is not None else 0
            customer_due = customer_row[2] if customer_row[2] is not None else 0
            # تحقق من الرصيد إذا لم يكن الدفع نقدي
            if payment_method != 'نقدي':
                if customer_balance >= total:
                    # الرصيد كافٍ: خصم من الرصيد فقط
                    QtWidgets.QMessageBox.information(
                        self,
                        'تفاصيل العملية',
                        f'تم خصم كامل قيمة الفاتورة ({total:.2f} ر.س) من رصيد العميل.\nالرصيد المتبقي: {customer_balance - total:.2f} ر.س.'
                    )
                else:
                    # الرصيد غير كافٍ: خصم الرصيد المتوفر وتسجيل الباقي كمستحق
                    due_to_add = total - customer_balance
                    new_due = customer_due + due_to_add
                    QtWidgets.QMessageBox.information(
                        self,
                        'تفاصيل العملية',
                        f'تم خصم الرصيد المتوفر بالكامل ({customer_balance:.2f} ر.س).\nالمتبقي ({due_to_add:.2f} ر.س) تم تسجيله كمستحق جديد.\nإجمالي المستحق الآن: {new_due:.2f} ر.س.'
                    )
                # لا تمنع البيع، فقط تنبيه احترافي
        from datetime import datetime
        sale_date = datetime.now().strftime('%Y-%m-%d')
        c.execute('INSERT INTO sales (customer_name, sale_date, total, payment_method) VALUES (?, ?, ?, ?)', (customer, sale_date, total, payment_method))
        sale_id = c.lastrowid
        for item in self.cart:
            c.execute('SELECT id FROM products WHERE name = ?', (item['product'],))
            product_id = c.fetchone()[0]
            c.execute('INSERT INTO sales_items (sale_id, product_id, quantity, sale_price, discount) VALUES (?, ?, ?, ?, ?)',
                      (sale_id, product_id, item['quantity'], item['price'], item.get('discount', 0)))
            c.execute('UPDATE inventory SET quantity = quantity - ? WHERE product_id = ?', (item['quantity'], product_id))

        # تحديث رصيد العميل والمستحقات بشكل احترافي
        if customer and payment_method != 'نقدي':
            if customer_balance >= total:
                # الرصيد كافٍ: خصم من الرصيد فقط
                c.execute('UPDATE customers SET balance = balance - ? WHERE id = ?', (total, customer_id))
            else:
                # الرصيد غير كافٍ: خصم الرصيد المتوفر وتسجيل الباقي كمستحق
                due_to_add = total - customer_balance
                c.execute('UPDATE customers SET balance = 0, due_amount = IFNULL(due_amount, 0) + ? WHERE id = ?', (due_to_add, customer_id))
        conn.commit()
        conn.close()
        # --- تحسين: طباعة الفاتورة تلقائياً بعد البيع مع خيار تأكيد ---
        reply = QtWidgets.QMessageBox.question(self, 'طباعة الفاتورة', 'تمت عملية البيع بنجاح!\nهل ترغب في طباعة الفاتورة الآن؟',
                                               QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No, QtWidgets.QMessageBox.Yes)
        if reply == QtWidgets.QMessageBox.Yes:
            self.print_current_invoice()
        self.cart.clear()
        self.refresh_cart()
        self.load_sales_log()  # تحديث جداول السجلات مباشرة بعد البيع
        self.status_label.setText('تمت عملية البيع بنجاح!')
        self.status_label.setStyleSheet('color: green')
        self.customer_combo.setCurrentIndex(-1)
        # إرسال إشارة تحديث لوحة التحكم بعد البيع
        event_bus.sale_made.emit()
    def showEvent(self, event):
        super().showEvent(event)
        self.refresh_customers()
        self.load_sales_log()

    def load_sales_log(self):
        """
        تحميل آخر عمليات البيع وعرضها في الجداول (الكل/عملاء/غير العملاء).
        يتم تطبيق ar_if_needed فقط عند العرض.
        """
        def fill_table(table, rows):
            table.setRowCount(len(rows))
            for i, row in enumerate(rows):
                for j, val in enumerate(row):
                    s = str(val) if val is not None else ''
                    item = QtWidgets.QTableWidgetItem(s)
                    # محاذاة النص العربي أو النصوص تلقائياً يمين/وسط
                    if any('\u0600' <= c <= '\u06FF' or '\u0750' <= c <= '\u077F' for c in s):
                        item.setTextAlignment(QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
                        item.setData(QtCore.Qt.TextAlignmentRole, QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter)
                    else:
                        item.setTextAlignment(QtCore.Qt.AlignCenter)
                        item.setData(QtCore.Qt.TextAlignmentRole, QtCore.Qt.AlignCenter)
                    table.setItem(i, j, item)
        try:
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            # الكل
            c.execute('SELECT sale_date, customer_name, total, payment_method FROM sales ORDER BY id DESC LIMIT 10')
            all_rows = c.fetchall() if c.description else []
            fill_table(self.sales_log, all_rows)
            # العملاء
            c.execute('SELECT sale_date, customer_name, total, payment_method FROM sales WHERE customer_name != "" ORDER BY id DESC LIMIT 10')
            customer_rows = c.fetchall() if c.description else []
            fill_table(self.sales_log_customers, customer_rows)
            # غير العملاء
            c.execute('SELECT sale_date, customer_name, total, payment_method FROM sales WHERE customer_name = "" ORDER BY id DESC LIMIT 10')
            other_rows = c.fetchall() if c.description else []
            fill_table(self.sales_log_others, other_rows)
            conn.close()
        except Exception as e:
            self.status_label.setText(f'خطأ في تحميل سجل المبيعات: {e}')
            self.status_label.setStyleSheet('color: red')

    def generate_invoice_html(self):
        """
        توليد HTML للفاتورة بناءً على محتوى السلة.
        """
        # --- تحسين: إضافة شعار وبيانات المحل وعبارة شكر ---
        shop_name = "مطعم/محل الثريا"
        shop_phone = "0500000000"
        shop_address = "الرياض - شارع العليا"
        from utils.print_utils import get_icon
        shop_logo = get_icon('smart.ico')
        items_html = ""
        for item in self.cart:
            items_html += f"<tr><td>{item['product']}</td><td>{item['quantity']}</td><td>{item['price']}</td><td>{item['quantity'] * item['price'] - item.get('discount', 0)}</td></tr>"
        html = f"""
        <html>
        <head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}} .header{{text-align:center}} .footer{{text-align:center;color:#1976d2;font-size:13pt;margin-top:18px}}</style></head>
        <body dir='rtl'>
        <div class='header'>
            <img src='{shop_logo}' width='64' height='64' style='border-radius:32px;border:2px solid #1976d2;margin-bottom:8px'><br>
            <span style='font-size:20pt;font-weight:bold;color:#1976d2'>{shop_name}</span><br>
            <span style='font-size:12pt;color:#555'>{shop_address} | {shop_phone}</span>
        </div>
        <h2 style='text-align:center'>فاتورة مبيعات</h2>
        <table><tr><th>المنتج</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th></tr>
        {items_html}
        </table>
        <h3 style='text-align:left'>الإجمالي: {self.get_total()} ريال</h3>
        <div class='footer'>شكراً لتعاملكم معنا!<br>نتمنى لكم يوماً سعيداً</div>
        </body></html>
        """
        return html

    def open_return_dialog(self):
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle('إرجاع منتج مباع')
        form = QtWidgets.QFormLayout(dialog)
        # اختيار الفاتورة
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute('SELECT id FROM sales ORDER BY id DESC LIMIT 50')
        sales_ids = [str(row[0]) for row in c.fetchall()]
        conn.close()
        sale_combo = QtWidgets.QComboBox()
        sale_combo.addItems(sales_ids)
        form.addRow('رقم الفاتورة:', sale_combo)
        # اختيار المنتج من الفاتورة
        product_combo = QtWidgets.QComboBox()
        def update_products():
            sale_id = sale_combo.currentText()
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('''SELECT p.name FROM sales_items si JOIN products p ON si.product_id = p.id WHERE si.sale_id = ?''', (sale_id,))
            products = [row[0] for row in c.fetchall()]
            conn.close()
            product_combo.clear()
            product_combo.addItems(products)
        sale_combo.currentIndexChanged.connect(update_products)
        update_products()
        form.addRow('المنتج:', product_combo)
        qty_spin = QtWidgets.QSpinBox()
        qty_spin.setRange(1, 10000)
        form.addRow('الكمية المرتجعة:', qty_spin)
        reason_edit = QtWidgets.QLineEdit()
        form.addRow('سبب الإرجاع:', reason_edit)
        btns = QtWidgets.QDialogButtonBox(QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel)
        form.addRow(btns)
        btns.accepted.connect(dialog.accept)
        btns.rejected.connect(dialog.reject)
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            sale_id = int(sale_combo.currentText())
            product_name = product_combo.currentText()
            qty = qty_spin.value()
            reason = reason_edit.text().strip()
            # تحقق من الكمية المباعة
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            c.execute('SELECT id FROM products WHERE name = ?', (product_name,))
            product_id = c.fetchone()[0]
            c.execute('SELECT quantity FROM sales_items WHERE sale_id = ? AND product_id = ?', (sale_id, product_id))
            sold_qty = c.fetchone()
            if not sold_qty or qty > sold_qty[0]:
                # رسالة عربية أصلية بدون أي معالجة نصية
                QtWidgets.QMessageBox.warning(self, 'خطأ', 'الكمية المرتجعة أكبر من الكمية المباعة!')
                conn.close()
                return
            # إضافة سجل المرتجع
            from datetime import datetime
            return_date = datetime.now().strftime('%Y-%m-%d')
            c.execute('INSERT INTO sales_returns (sale_id, product_id, quantity, return_date, reason) VALUES (?, ?, ?, ?, ?)',
                      (sale_id, product_id, qty, return_date, reason))
            # تحديث المخزون
            c.execute('UPDATE inventory SET quantity = quantity + ? WHERE product_id = ?', (qty, product_id))
            conn.commit()
            conn.close()
            # رسالة عربية أصلية بدون أي معالجة نصية
            QtWidgets.QMessageBox.information(self, 'تم', 'تم تسجيل المرتجع وتحديث المخزون بنجاح.')

    def show_sales_report_window(self):
        """
        نافذة تقارير المبيعات الاحترافية: ملخص، المنتجات الأكثر مبيعًا، تنبيهات المخزون والمبيعات.
        """
        report_window = QtWidgets.QDialog(self)
        report_window.setWindowTitle('تقارير المبيعات')
        report_window.setGeometry(500, 200, 800, 600)
        layout = QtWidgets.QVBoxLayout()
        # --- ملخص المبيعات ---
        summary_label = QtWidgets.QLabel('ملخص المبيعات')
        summary_label.setStyleSheet('font-size: 15pt; font-weight: bold; color: #1976d2;')
        layout.addWidget(summary_label)
        try:
            conn = sqlite3.connect(database.DB_NAME)
            c = conn.cursor()
            # إجمالي المبيعات وعدد الفواتير
            c.execute('SELECT COUNT(*), IFNULL(SUM(total),0), IFNULL(AVG(total),0) FROM sales')
            count, total, avg = c.fetchone()
            # مبيعات اليوم
            from datetime import datetime
            today = datetime.now().strftime('%Y-%m-%d')
            c.execute('SELECT IFNULL(SUM(total),0) FROM sales WHERE sale_date = ?', (today,))
            today_total = c.fetchone()[0]
            # أكثر المنتجات مبيعًا
            c.execute('''SELECT p.name, SUM(si.quantity) as total_qty FROM sales_items si JOIN products p ON si.product_id = p.id GROUP BY si.product_id ORDER BY total_qty DESC LIMIT 5''')
            top_products = c.fetchall()
            # المنتجات منخفضة المخزون
            c.execute('''SELECT p.name, IFNULL(i.quantity,0), p.min_quantity FROM products p LEFT JOIN inventory i ON p.id = i.product_id WHERE IFNULL(i.quantity,0) <= p.min_quantity ORDER BY i.quantity ASC LIMIT 5''')
            low_stock = c.fetchall()
            # مبيعات آخر 7 أيام
            c.execute('''SELECT sale_date, IFNULL(SUM(total),0) FROM sales WHERE sale_date >= date('now', '-6 days') GROUP BY sale_date ORDER BY sale_date DESC''')
            last7 = c.fetchall()
            conn.close()
        except Exception as e:
            layout.addWidget(QtWidgets.QLabel(f'<span style="color:red">خطأ في جلب البيانات: {e}</span>'))
            close_btn = QtWidgets.QPushButton('إغلاق')
            close_btn.clicked.connect(report_window.close)
            layout.addWidget(close_btn)
            report_window.setLayout(layout)
            report_window.exec_()
            return
        # --- عرض ملخص ---
        summary_text = f"<ul style='font-size:13pt'>"
        summary_text += f"<li>إجمالي المبيعات: <b>{total:.2f} ر.س</b></li>"
        summary_text += f"<li>عدد الفواتير: <b>{count}</b></li>"
        summary_text += f"<li>متوسط قيمة الفاتورة: <b>{avg:.2f} ر.س</b></li>"
        summary_text += f"<li>مبيعات اليوم: <b>{today_total:.2f} ر.س</b></li>"
        summary_text += "</ul>"
        layout.addWidget(QtWidgets.QLabel(summary_text))
        # --- أكثر المنتجات مبيعًا ---
        top_label = QtWidgets.QLabel('أكثر المنتجات مبيعًا:')
        top_label.setStyleSheet('font-size: 13pt; font-weight: bold; color: #263238;')
        layout.addWidget(top_label)
        top_table = QtWidgets.QTableWidget()
        top_table.setColumnCount(2)
        top_table.setHorizontalHeaderLabels(['المنتج', 'إجمالي الكمية المباعة'])
        top_table.setRowCount(len(top_products))
        for i, (name, qty) in enumerate(top_products):
            top_table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(name)))
            top_table.setItem(i, 1, QtWidgets.QTableWidgetItem(str(qty)))
        top_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(top_table)
        # --- المنتجات منخفضة المخزون ---
        low_label = QtWidgets.QLabel('منتجات منخفضة المخزون:')
        low_label.setStyleSheet('font-size: 13pt; font-weight: bold; color: #b71c1c;')
        layout.addWidget(low_label)
        low_table = QtWidgets.QTableWidget()
        low_table.setColumnCount(3)
        low_table.setHorizontalHeaderLabels(['المنتج', 'المتوفر', 'الحد الأدنى'])
        low_table.setRowCount(len(low_stock))
        for i, (name, qty, minq) in enumerate(low_stock):
            low_table.setItem(i, 0, QtWidgets.QTableWidgetItem(str(name)))
            low_table.setItem(i, 1, QtWidgets.QTableWidgetItem(str(qty)))
            low_table.setItem(i, 2, QtWidgets.QTableWidgetItem(str(minq)))
        low_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(low_table)
        # --- رسم بياني مبسط لمبيعات آخر 7 أيام (نص فقط) ---
        chart_label = QtWidgets.QLabel('مبيعات آخر 7 أيام:')
        chart_label.setStyleSheet('font-size: 13pt; font-weight: bold; color: #263238;')
        layout.addWidget(chart_label)
        chart_text = ""
        if last7:
            for date, val in last7:
                chart_text += f"<b>{date}</b>: {val:.2f} ر.س<br>"
        else:
            chart_text = "لا توجد بيانات مبيعات للأيام الأخيرة."
        layout.addWidget(QtWidgets.QLabel(chart_text))
        # --- تنبيهات ---
        alerts = []
        if count == 0:
            alerts.append('<span style="color:red">لا توجد عمليات بيع مسجلة حتى الآن.</span>')
        if today_total < (avg * 0.5) and count > 0:
            alerts.append('<span style="color:orange">تنبيه: مبيعات اليوم أقل من نصف متوسط الفاتورة المعتاد.</span>')
        if len(low_stock) > 0:
            alerts.append('<span style="color:#b71c1c">تنبيه: هناك منتجات منخفضة المخزون!</span>')
        if not alerts:
            alerts.append('<span style="color:green">لا توجد تنبيهات حالية.</span>')
        alert_label = QtWidgets.QLabel('التنبيهات:')
        alert_label.setStyleSheet('font-size: 13pt; font-weight: bold; color: #d84315;')
        layout.addWidget(alert_label)
        for alert in alerts:
            layout.addWidget(QtWidgets.QLabel(alert))
        # --- زر إغلاق ---
        close_btn = QtWidgets.QPushButton('إغلاق')
        close_btn.setStyleSheet('background-color: #263238; color: white; font-weight: bold;')
        close_btn.clicked.connect(report_window.close)
        layout.addWidget(close_btn)
        report_window.setLayout(layout)
        report_window.exec_()

    def print_current_invoice(self):
        """
        توليد HTML للفاتورة الحالية وطباعتها باستخدام print_invoice.
        """
        from utils.print_utils import print_invoice
        html = self.generate_invoice_html()  # يجب أن تكون لديك دالة تولد HTML للفاتورة
        print_invoice(html, self)

    def get_total(self):
        """
        حساب إجمالي الفاتورة الحالية (بعد الخصومات).
        """
        total = 0
        for item in self.cart:
            total += item['quantity'] * item['price'] - item.get('discount', 0)
        return total

    # تم حذف التكرار: دالة generate_invoice_html موجودة بالفعل أعلاه