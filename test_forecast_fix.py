#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات قسم توقع المبيعات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_forecast_functions():
    """
    اختبار دوال توقع المبيعات للتأكد من عدم وجود أخطاء
    """
    try:
        # استيراد الوحدات المطلوبة
        from views.statistics_view import StatisticsWindow
        import sqlite3
        from models import database
        
        print("✓ تم استيراد الوحدات بنجاح")
        
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists(database.DB_NAME):
            print("⚠ قاعدة البيانات غير موجودة، سيتم إنشاء اختبار وهمي")
            return True
        
        # اختبار الاتصال بقاعدة البيانات
        conn = sqlite3.connect(database.DB_NAME)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول المبيعات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sales'")
        if not cursor.fetchone():
            print("⚠ جدول المبيعات غير موجود")
            conn.close()
            return True
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM sales")
        count = cursor.fetchone()[0]
        print(f"✓ عدد المبيعات في قاعدة البيانات: {count}")
        
        conn.close()
        
        # اختبار دالة ar_text
        from views.statistics_view import ar_text
        test_text = ar_text("اختبار النص العربي")
        print(f"✓ دالة ar_text تعمل بشكل صحيح: {test_text}")
        
        print("✓ جميع الاختبارات نجحت!")
        return True
        
    except ImportError as e:
        print(f"✗ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"✗ خطأ عام: {e}")
        return False

def test_prophet_availability():
    """
    اختبار توفر مكتبة Prophet
    """
    try:
        from prophet import Prophet
        print("✓ مكتبة Prophet متوفرة")
        return True
    except ImportError:
        print("⚠ مكتبة Prophet غير مثبتة - سيظهر رسالة خطأ مناسبة للمستخدم")
        return False

def test_sklearn_availability():
    """
    اختبار توفر مكتبة scikit-learn
    """
    try:
        from sklearn.cluster import KMeans
        print("✓ مكتبة scikit-learn متوفرة")
        return True
    except ImportError:
        print("⚠ مكتبة scikit-learn غير مثبتة - سيظهر رسالة خطأ مناسبة للمستخدم")
        return False

if __name__ == "__main__":
    print("🔍 بدء اختبار إصلاحات توقع المبيعات...")
    print("=" * 50)
    
    # تشغيل الاختبارات
    test1 = test_forecast_functions()
    test2 = test_prophet_availability()
    test3 = test_sklearn_availability()
    
    print("=" * 50)
    if test1:
        print("✅ الاختبارات الأساسية نجحت - الإصلاحات تعمل بشكل صحيح")
    else:
        print("❌ فشلت بعض الاختبارات - يرجى مراجعة الأخطاء أعلاه")
    
    if not test2:
        print("📝 ملاحظة: لاستخدام توقع المبيعات، قم بتثبيت Prophet:")
        print("   pip install prophet")
    
    if not test3:
        print("📝 ملاحظة: لاستخدام التحليل العنقودي، قم بتثبيت scikit-learn:")
        print("   pip install scikit-learn")
