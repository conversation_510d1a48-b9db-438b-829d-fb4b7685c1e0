2025-07-22 20:48:48 - RestaurantApp - INFO - logger.py:112 - بدء تشغيل التطبيق
2025-07-22 20:48:49 - RestaurantApp - INFO - logger.py:112 - تهيئة قاعدة البيانات
2025-07-22 20:48:50 - RestaurantApp - CRITICAL - logger.py:142 - خطأ حرج في بدء التطبيق - الخطأ الحرج: No module named 'schedule'
تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\appazam\resterant2\main.py", line 110, in <module>
    from utils.backup_manager import backup_manager
  File "c:\appazam\resterant2\utils\backup_manager.py", line 10, in <module>
    import schedule
ModuleNotFoundError: No module named 'schedule'

2025-07-22 20:48:50 - RestaurantApp - CRITICAL - logger.py:142 - استثناء غير معالج في التطبيق - الخطأ الحرج: No module named 'schedule'
تفاصيل الخطأ:
NoneType: None
 - البيانات الإضافية: {'type': 'ModuleNotFoundError', 'traceback': '  File "c:\\appazam\\resterant2\\main.py", line 110, in <module>\n    from utils.backup_manager import backup_manager\n  File "c:\\appazam\\resterant2\\utils\\backup_manager.py", line 10, in <module>\n    import schedule\n'}
2025-07-22 20:51:54 - RestaurantApp - INFO - logger.py:112 - بدء تشغيل التطبيق
2025-07-22 20:51:54 - RestaurantApp - INFO - logger.py:112 - تهيئة قاعدة البيانات
2025-07-22 20:51:55 - RestaurantApp - CRITICAL - logger.py:142 - خطأ حرج في بدء التطبيق - الخطأ الحرج: 'Job' object has no attribute 'month'
تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\appazam\resterant2\main.py", line 110, in <module>
    from utils.backup_manager import backup_manager
  File "c:\appazam\resterant2\utils\backup_manager.py", line 376, in <module>
    backup_manager = BackupManager()
  File "c:\appazam\resterant2\utils\backup_manager.py", line 47, in __init__
    self.start_auto_backup()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\appazam\resterant2\utils\backup_manager.py", line 343, in start_auto_backup
    schedule.every().month.do(self._scheduled_backup, 'monthly')
    ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Job' object has no attribute 'month'

2025-07-22 20:51:55 - RestaurantApp - CRITICAL - logger.py:142 - استثناء غير معالج في التطبيق - الخطأ الحرج: 'Job' object has no attribute 'month'
تفاصيل الخطأ:
NoneType: None
 - البيانات الإضافية: {'type': 'AttributeError', 'traceback': '  File "c:\\appazam\\resterant2\\main.py", line 110, in <module>\n    from utils.backup_manager import backup_manager\n  File "c:\\appazam\\resterant2\\utils\\backup_manager.py", line 376, in <module>\n    backup_manager = BackupManager()\n  File "c:\\appazam\\resterant2\\utils\\backup_manager.py", line 47, in __init__\n    self.start_auto_backup()\n    ~~~~~~~~~~~~~~~~~~~~~~^^\n  File "c:\\appazam\\resterant2\\utils\\backup_manager.py", line 343, in start_auto_backup\n    schedule.every().month.do(self._scheduled_backup, \'monthly\')\n    ^^^^^^^^^^^^^^^^^^^^^^\n'}
