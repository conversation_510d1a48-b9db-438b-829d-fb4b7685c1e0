# نافذة تحليل سلوك العملاء (تجميع ذكي)
# تعتمد على KMeans لتصنيف العملاء إلى مجموعات حسب سلوك الشراء
from PyQt5 import QtWidgets, QtCore, QtGui
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import matplotlib.pyplot as plt
import pandas as pd
import sqlite3
import os
import arabic_reshaper
from bidi.algorithm import get_display
import matplotlib
from main import get_app_icon
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['font.family'] = ['Cairo', 'Amiri', 'Arial', 'Tahoma', 'sans-serif']

def ar_text(text):
    reshaped = arabic_reshaper.reshape(text)
    return get_display(reshaped)

class CustomerSegmentationWindow(QtWidgets.QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('تحليل سلوك العملاء')
        self.setGeometry(520, 260, 700, 500)
        self.setWindowIcon(get_app_icon())
        self.setup_ui()
        self.load_and_cluster()

    def setup_ui(self):
        layout = QtWidgets.QVBoxLayout(self)
        self.figure = plt.Figure(figsize=(7,4))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        self.status_label = QtWidgets.QLabel('')
        layout.addWidget(self.status_label)
        print_btn = QtWidgets.QPushButton('طباعة التحليل')
        print_btn.clicked.connect(self.print_current_segmentation)
        layout.addWidget(print_btn)

    def load_and_cluster(self):
        # جلب بيانات العملاء وعدد/قيمة مشترياتهم
        from models import database
        conn = sqlite3.connect(database.DB_NAME)
        df = pd.read_sql_query('''SELECT c.name, COUNT(s.id) as num_orders, SUM(s.total) as total_spent FROM customers c LEFT JOIN sales s ON c.name=s.customer_name GROUP BY c.name''', conn)
        conn.close()
        if len(df) < 3:
            self.status_label.setText('لا توجد بيانات كافية لتحليل التجميع.')
            return
        try:
            from sklearn.cluster import KMeans
            X = df[['num_orders', 'total_spent']].fillna(0)
            kmeans = KMeans(n_clusters=3, random_state=42)
            df['cluster'] = kmeans.fit_predict(X)
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            colors = ['#1976d2', '#43a047', '#e53935']
            for i in range(3):
                group = df[df['cluster'] == i]
                ax.scatter(group['num_orders'], group['total_spent'], label=ar_text(f'مجموعة {i+1}'), color=colors[i], s=80)
            ax.set_xlabel(ar_text('عدد الطلبات'), fontsize=14)
            ax.set_ylabel(ar_text('إجمالي الإنفاق'), fontsize=14)
            ax.set_title(ar_text('تصنيف العملاء حسب السلوك الشرائي'), fontsize=16, fontweight='bold')
            ax.legend(loc='upper right', fontsize=12)
            self.figure.tight_layout()
            self.canvas.draw()
            self.status_label.setText('تم تصنيف العملاء إلى 3 مجموعات بناءً على السلوك.')
        except ImportError:
            self.status_label.setText('مطلوب تثبيت مكتبة scikit-learn.')
        except Exception as e:
            self.status_label.setText(f'فشل التحليل: {e}')

    def print_current_segmentation(self):
        from utils.print_utils import print_invoice
        html = self.generate_segmentation_html()
        print_invoice(html, self)

    def generate_segmentation_html(self):
        items_html = ""
        for row in self.get_segmentation_rows():
            items_html += f"<tr>{''.join(f'<td>{cell}</td>' for cell in row)}</tr>"
        html = f"""
        <html><head><meta charset='utf-8'><style>table{{width:100%;border-collapse:collapse}}td,th{{border:1px solid #888;padding:4px}}</style></head><body dir='rtl'>
        <h2 style='text-align:center'>تحليل سلوك العملاء</h2>
        <table>{items_html}</table></body></html>"""
        return html

    def get_segmentation_rows(self):
        # جلب بيانات العملاء وعدد/قيمة مشترياتهم
        from models import database
        import pandas as pd
        import sqlite3
        conn = sqlite3.connect(database.DB_NAME)
        df = pd.read_sql_query('''SELECT c.name, COUNT(s.id) as num_orders, SUM(s.total) as total_spent FROM customers c LEFT JOIN sales s ON c.name=s.customer_name GROUP BY c.name''', conn)
        conn.close()
        if len(df) < 3:
            return []
        try:
            from sklearn.cluster import KMeans
            X = df[['num_orders', 'total_spent']].fillna(0)
            kmeans = KMeans(n_clusters=3, random_state=42)
            df['cluster'] = kmeans.fit_predict(X)
            # إعادة النتائج مع رقم المجموعة (1,2,3)
            rows = []
            for _, row in df.iterrows():
                rows.append([row['name'], int(row['num_orders']), float(row['total_spent']) if row['total_spent'] else 0, f"مجموعة {int(row['cluster'])+1}"])
            return rows
        except Exception as e:
            return []
