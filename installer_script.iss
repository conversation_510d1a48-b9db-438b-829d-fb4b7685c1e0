; سكريبت Inno Setup لتثبيت برنامج "نضام محاسبي"
; احفظ هذا الملف بامتداد .iss وافتحه ببرنامج Inno Setup Compiler

[Setup]
AppName=نضام محاسبي
AppVersion=1.0
DefaultDirName={pf}\نضام محاسبي
DefaultGroupName=نضام محاسبي
UninstallDisplayIcon={app}\نضام محاسبي.exe
Compression=lzma
SolidCompression=yes
OutputDir=dist_installer
OutputBaseFilename=نضام_محاسبي_تثبيت
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"

[Files]
Source: "dist\نضام محاسبي\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{commondesktop}\نضام محاسبي"; Filename: "{app}\نضام محاسبي.exe"; WorkingDir: "{app}"
Name: "{group}\نضام محاسبي"; Filename: "{app}\نضام محاسبي.exe"; WorkingDir: "{app}"

[Run]
Filename: "{app}\نضام محاسبي.exe"; Description: "تشغيل نضام محاسبي"; Flags: nowait postinstall skipifsilent
