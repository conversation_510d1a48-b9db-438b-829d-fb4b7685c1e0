# ملف تسجيل الدخول للبرنامج
# يعرض نافذة تسجيل الدخول ويتحقق من بيانات المستخدم
# في حال نجاح الدخول يفتح لوحة التحكم الرئيسية
# تاريخ آخر تعديل: 2025-05-03

from PyQt5 import QtWidgets, QtGui, QtCore
import sqlite3
from models import database
import sys
import hashlib
import os
from utils.messages import get_message
from main import get_app_icon

class LoginWindow(QtWidgets.QWidget):
    def __init__(self, labels=None):
        super().__init__()
        self.labels = labels or {
            'PRODUCT_LABEL': 'منتج',
            'CUSTOMER_LABEL': 'عميل',
            'SUPPLIER_LABEL': 'مورد',
            'BUSINESS_TYPE': 'نشاط'
        }
        self.setWindowTitle('تسجيل الدخول - نظام محاسبي ')
        self.setGeometry(500, 250, 350, 250)
        self.setWindowIcon(get_app_icon())
        self.setup_ui()

    def setup_ui(self):
        """
        إعداد واجهة المستخدم لنافذة تسجيل الدخول مع تصميم عصري وألوان ذهبية.
        """
        self.setStyleSheet('''
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1976d2, stop:1 #42a5f5);
                border-radius: 18px;
            }
            QLabel#titleLabel {
                color: #FFD700;
                font-size: 22pt;
                font-weight: bold;
                letter-spacing: 2px;
                text-shadow: 1px 1px 6px #000;
            }
            QLineEdit {
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 7px 12px;
                font-size: 13pt;
                background: #fffbe6;
                color: #232526;
            }
            QLineEdit:focus {
                border: 2.5px solid #FFC300;
                background: #fffde4;
            }
            QPushButton {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #FFD700, stop:1 #FFC300);
                color: #232526;
                font-size: 15pt;
                font-weight: bold;
                border-radius: 12px;
                padding: 8px 0;
                margin-top: 10px;
                box-shadow: 0 2px 8px #bfa10044;
            }
            QPushButton:hover {
                background-color: #FFC300;
                color: #fff;
            }
            QComboBox {
                border: 2px solid #FFD700;
                border-radius: 10px;
                padding: 5px 10px;
                font-size: 12pt;
                background: #fffbe6;
                color: #232526;
            }
            QLabel#errorLabel {
                color: #e53935;
                font-weight: bold;
            }
        ''')
        layout = QtWidgets.QVBoxLayout()
        layout.setContentsMargins(30, 25, 30, 25)
        layout.setSpacing(13)
        # اختيار نوع النشاط
        self.activity_combo = QtWidgets.QComboBox()
        self.activity_combo.addItems(['نضام محاسبي ذكي', 'صيدلية'])
        layout.addWidget(QtWidgets.QLabel('نوع النشاط:'))
        layout.addWidget(self.activity_combo)
        # عنوان النافذة
        title = QtWidgets.QLabel('تسجيل الدخول')
        title.setObjectName('titleLabel')
        title.setFont(QtGui.QFont('Cairo', 22, QtGui.QFont.Bold))
        title.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(title)
        # حقل اسم المستخدم
        self.username = QtWidgets.QLineEdit()
        self.username.setPlaceholderText('اسم المستخدم')
        layout.addWidget(self.username)
        # حقل كلمة المرور
        self.password = QtWidgets.QLineEdit()
        self.password.setPlaceholderText('كلمة المرور')
        self.password.setEchoMode(QtWidgets.QLineEdit.Password)
        layout.addWidget(self.password)
        # رسالة الخطأ
        self.error_label = QtWidgets.QLabel('')
        self.error_label.setObjectName('errorLabel')
        layout.addWidget(self.error_label)
        # زر الدخول
        login_btn = QtWidgets.QPushButton('دخول')
        login_btn.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        login_btn.clicked.connect(self.handle_login)
        layout.addWidget(login_btn)
        self.setLayout(layout)

    def handle_login(self):
        """
        التحقق من بيانات المستخدم وتسجيل الدخول إذا كانت صحيحة، مع حفظ نوع النشاط.
        """
        # حفظ نوع النشاط المختار في ملف activity.txt
        activity_map = {'نضام محاسبي ذكي': 'restaurant', 'صيدلية': 'pharmacy'}
        selected = self.activity_combo.currentText()
        activity_code = activity_map.get(selected, 'restaurant')
        with open('activity.txt', 'w', encoding='utf-8') as f:
            f.write(activity_code)
        # إعادة تحميل الإعدادات الصحيحة بعد تغيير النشاط
        from utils.utilities import load_settings
        settings = load_settings(activity_code)
        self.labels = {
            'PRODUCT_LABEL': settings.get('product_label', 'منتج'),
            'CUSTOMER_LABEL': settings.get('customer_label', 'عميل'),
            'SUPPLIER_LABEL': settings.get('supplier_label', 'مورد'),
            'BUSINESS_TYPE': settings.get('business_type', 'نشاط'),
            'UNIT_OPTIONS': settings.get('unit_options', []),
            'EXPIRY_ALERT_DAYS': settings.get('expiry_alert_days', 30)
        }
        username = self.username.text().strip()
        password = self.password.text().strip()
        if not username or not password:
            self.error_label.setText(get_message('error_required_fields'))
            return
        conn = sqlite3.connect(database.DB_NAME)
        c = conn.cursor()
        c.execute("SELECT id, password, role, permissions FROM users WHERE username = ?", (username,))
        result = c.fetchone()
        if result:
            user_id, password_hash, role, permissions = result
            if hashlib.sha256(password.encode()).hexdigest() == password_hash:
                self.error_label.setStyleSheet('color: green')
                self.error_label.setText('تم تسجيل الدخول بنجاح!')
                # فحص المنتجات المنتهية أو القريبة من الانتهاء
                from datetime import datetime, timedelta
                today = datetime.today().date()
                near_days = 7  # عدد الأيام للتنبيه
                c.execute('''SELECT p.name, i.expiry_date FROM inventory i JOIN products p ON i.product_id = p.id WHERE i.expiry_date IS NOT NULL AND i.expiry_date != '' ''')
                rows = c.fetchall()
                expired = []
                near_expiry = []
                for name, expiry in rows:
                    try:
                        exp_date = datetime.strptime(expiry, '%Y-%m-%d').date()
                        if exp_date < today:
                            expired.append(name)
                        elif (exp_date - today).days <= near_days:
                            near_expiry.append(f"{name} (ينتهي خلال {(exp_date - today).days} يوم)")
                    except:
                        continue
                msg = ''
                if expired:
                    msg += 'منتجات منتهية الصلاحية:\n' + '\n'.join(expired) + '\n\n'
                if near_expiry:
                    msg += 'منتجات ستنتهي قريبًا:\n' + '\n'.join(near_expiry)
                if msg:
                    QtWidgets.QMessageBox.warning(self, 'تنبيه صلاحية المنتجات', msg)
                from views.dashboard_view import DashboardWindow
                # تمرير الصلاحيات إلى لوحة التحكم
                self.dashboard = DashboardWindow(username, role, permissions=permissions, user_id=user_id, labels=self.labels)
                self.dashboard.show()
                self.close()
                conn.close()
                return
        conn.close()
        self.error_label.setStyleSheet('color: red')
        self.error_label.setText(get_message('error_login'))

# دالة تشغيل البرنامج من نافذة تسجيل الدخول

def run(LABELS=None):
    database.init_db()  # تهيئة قاعدة البيانات
    app = QtWidgets.QApplication.instance() or QtWidgets.QApplication(sys.argv)
    # تطبيق QDarkStyle لمظهر عصري
    try:
        import qdarkstyle
        app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt5())
    except Exception as e:
        print('تعذر تحميل QDarkStyle:', e)
    # يمكن دمج QSS مخصص بعد QDarkStyle إذا رغبت
    try:
        with open('resources/styles/app_style.qss', encoding='utf-8') as f:
            app.setStyleSheet(app.styleSheet() + f.read())
    except Exception as e:
        print('تعذر تحميل ملف التنسيقات:', e)
    window = LoginWindow(labels=LABELS)
    window.show()
    app.exec_()  # استخدم app.exec_() فقط بدون sys.exit/app.exit/app.quit