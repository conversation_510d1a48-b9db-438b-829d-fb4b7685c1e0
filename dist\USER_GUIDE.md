- دليل المستخدم الشامل لبرنامج إدارة المطعم

1 مقدمة

هذا البرنامج هو نظام متكامل لإدارة المطاعم (ويمكن تكييفه لإدارة أنشطة أخرى مثل الصيدليات أو السوبرماركت) مبني باستخدام لغة بايثون وإطار PyQt5 لواجهة المستخدم الرسومية. يوفر النظام إدارة متقدمة للمنتجات، العملاء، الموردين، المبيعات، المشتريات، المخزون، التقارير، التحليلات، والمزيد.

---

2 أقسام ومجلدات المشروع

- **main.py**: ملف التشغيل الرئيسي للبرنامج.
- **models/**: يحتوي على ملفات التعامل مع قاعدة البيانات (مثل `database.py`).
- **utils/**: أدوات مساعدة مثل الرسائل، الطباعة، الأدوات العامة، إلخ.
- **views/**: جميع نوافذ واجهة المستخدم (تسجيل الدخول، العملاء، المنتجات، التقارير، إلخ).
- **resources/**: الموارد مثل الأيقونات والصور.
- **styles/**: ملفات تنسيق الواجهة (QSS).
- **requirements.txt**: قائمة بالمكتبات المطلوبة.
- **settings.json**: إعدادات البرنامج العامة.
- **restaurant.db**: قاعدة البيانات (SQLite).

---

3 شرح عمل البرنامج

1. **تشغيل البرنامج**:

   - يبدأ التنفيذ من `main.py`.
   - يتم تحميل الإعدادات المناسبة حسب النشاط (مطعم/صيدلية/سوبرماركت).
   - تهيئة قاعدة البيانات وإنشاء الجداول إذا لم تكن موجودة.
   - تعيين الخط والاتجاه (يمين-يسار) ودعم اللغة العربية.
   - تعيين الأيقونة وتطبيق تنسيق الواجهة.
   - فتح نافذة تسجيل الدخول (`views/login_view.py`).

2. **تسجيل الدخول**:

   - يتم التحقق من بيانات المستخدم.
   - عند النجاح، يتم الانتقال إلى لوحة التحكم الرئيسية.

3. **إدارة المنتجات والعملاء والموردين**:

   - إضافة/تعديل/حذف المنتجات والعملاء والموردين.
   - البحث والاستعراض عبر واجهات رسومية سهلة.

4. **المبيعات والمشتريات**:

   - تسجيل عمليات البيع والشراء.
   - تحديث المخزون تلقائياً.

5. **التقارير والإحصائيات**:

   - تقارير مفصلة عن المبيعات، الأرباح، المخزون، العملاء، إلخ.
   - تحليلات ورسوم بيانية (عند توفرها).

6. **المساعد الذكي (اختياري)**:
   - دعم تكامل مع OpenAI API لبعض ميزات الذكاء الاصطناعي (يتطلب مفتاح API).

---

4 شرح أهم الدوال والعمليات

- **load_settings (utils/utilities.py)**: تحميل إعدادات البرنامج من ملف JSON.
- **get_message (utils/messages.py)**: جلب رسائل نصية مهيأة للواجهة.
- **init_db (models/database.py)**: تهيئة قاعدة البيانات وإنشاء الجداول.
- **run (views/login_view.py)**: تشغيل نافذة تسجيل الدخول.
- **show_fatal_error (main.py)**: عرض رسالة خطأ حرجة عند حدوث استثناء غير متوقع.

---

5 المكتبات الخارجية المستخدمة

- **PyQt5**: لبناء واجهة المستخدم الرسومية.
- **sqlite3**: قاعدة البيانات المحلية.
- **json**: التعامل مع ملفات الإعدادات.
- **os, sys**: التعامل مع النظام والبيئة.
- **traceback**: تتبع الأخطاء.
- **(اختياري) openai**: دعم الذكاء الاصطناعي.

---

6 الإعدادات وقاعدة البيانات

- إعدادات البرنامج في `settings.json` (أو ملف النشاط المناسب).
- قاعدة البيانات في `restaurant.db` (SQLite).
- يمكن تخصيص أسماء الحقول (منتج/عميل/مورد) من الإعدادات.

---

7 خطوات التشغيل

1. تثبيت بايثون (يفضل Python 3.8 أو أحدث).
2. تثبيت المتطلبات:
   ```bash
   pip install -r requirements.txt
   ```
3. تشغيل البرنامج:
   ```bash
   python main.py
   ```

---

## نصائح ودعم فني

- تأكد من وجود جميع ملفات الإعدادات وقاعدة البيانات.
- لتفعيل ميزات الذكاء الاصطناعي، أضف متغير البيئة `OPENAI_API_KEY`.
- في حال ظهور أي خطأ، راجع رسائل الخطأ أو تواصل مع الدعم الفني.

---

## الأسئلة الشائعة

- **كيف أغير النشاط (مطعم/صيدلية)؟**
  - عدل محتوى ملف `activity.txt` ليحتوي على اسم النشاط المطلوب (restaurant/pharmacy/supermarket/clinic).
- **كيف أعدل الإعدادات؟**
  - عدل ملف الإعدادات المناسب (`settings.json` أو غيره).
- **كيف أضيف مستخدمين جدد؟**
  - من واجهة تسجيل الدخول أو إعدادات المستخدمين (حسب توفرها).

---

لأي استفسار إضافي، يرجى التواصل مع مطور النظام أو الدعم الفني.
